#!/usr/bin/env python3
"""
Test the ASKCOS API directly to see what's happening.
"""

import requests
import json
from config.settings import Config

def test_askcos_api():
    """Test ASKCOS API directly."""
    print("🧪 Testing ASKCOS API directly...")
    
    config = Config()
    base_url = config.ASKOS_PREDICTIONS_PISTACHO_URL
    
    print(f"API URL: {base_url}")
    
    # Test with simple molecule first
    test_molecules = [
        "CCO",  # Ethanol
        "CC(=O)O",  # Acetic acid
        "O=C(O)C1=CC=C(C(=C1Cl)COCC(F)(F)F)S(=O)(=O)C"  # Your complex molecule
    ]
    
    for smiles in test_molecules:
        print(f"\n🔍 Testing: {smiles}")
        
        payload = {
            "smiles": smiles,
            "num_results": 50,
            "max_depth": 1
        }
        
        try:
            response = requests.post(
                base_url,
                json=payload,
                timeout=30
            )
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"Response keys: {list(data.keys())}")
                
                if 'result' in data:
                    results = data['result']
                    print(f"Number of results: {len(results)}")
                    
                    if results:
                        print("Sample results:")
                        for i, result in enumerate(results[:3]):
                            print(f"  {i+1}. Keys: {list(result.keys())}")
                            if 'smiles' in result:
                                print(f"     SMILES: {result['smiles']}")
                            if 'plausibility' in result:
                                print(f"     Plausibility: {result['plausibility']}")
                            if 'template' in result:
                                print(f"     Template: {result['template']}")
                    else:
                        print("❌ No results returned")
                else:
                    print("❌ No 'result' key in response")
                    print(f"Full response: {json.dumps(data, indent=2)}")
            else:
                print(f"❌ API call failed: {response.status_code}")
                print(f"Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Error calling API: {e}")

def test_distributed_service_api():
    """Test the distributed service's SingleStepAPI."""
    print("\n🌐 Testing Distributed Service SingleStepAPI...")
    
    # We need to test this from within the distributed service
    # Let's create a simple test endpoint
    
    test_payload = {
        "action": "test_single_step_api",
        "smiles": "CCO"
    }
    
    try:
        # This would require adding a test endpoint to the distributed service
        print("⚠️  Need to add test endpoint to distributed service")
        
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Run API tests."""
    print("🔍 Testing ASKCOS API Integration")
    print("=" * 50)
    
    test_askcos_api()
    test_distributed_service_api()

if __name__ == "__main__":
    main()
