#!/usr/bin/env python3
"""
Setup script for distributed tree search integration.
This script helps you configure and test the distributed search service.
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path

def check_requirements():
    """Check if required packages are installed."""
    required_packages = ['requests', 'redis', 'celery', 'flask']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing required packages: {', '.join(missing_packages)}")
        print("Install them with: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ All required packages are installed")
    return True

def check_redis():
    """Check if Redis is running."""
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, decode_responses=True)
        r.ping()
        print("✅ Redis is running")
        return True
    except Exception as e:
        print(f"❌ Redis is not running: {e}")
        print("Start Redis with: docker run -d -p 6379:6379 redis:alpine")
        return False

def setup_environment():
    """Setup environment variables for distributed search."""
    env_file = Path('.env')
    
    # Create .env file if it doesn't exist
    if not env_file.exists():
        env_content = """# Distributed Tree Search Configuration
USE_DISTRIBUTED_SEARCH=true
DISTRIBUTED_SEARCH_URL=http://localhost:8080

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Worker Configuration
MAX_MOLECULE_WORKERS=3
MAX_REACTION_WORKERS=5
MOLECULE_WORKER_CONCURRENCY=4
REACTION_WORKER_CONCURRENCY=8

# Processing Configuration
MAX_DEPTH=3
BEAM_WIDTH=50
PRUNING_FACTOR=0.1
MAX_ROUTES=100
"""
        with open(env_file, 'w') as f:
            f.write(env_content)
        print("✅ Created .env file with distributed search configuration")
    else:
        print("✅ .env file already exists")
    
    # Set environment variables for current session
    os.environ['USE_DISTRIBUTED_SEARCH'] = 'true'
    os.environ['DISTRIBUTED_SEARCH_URL'] = 'http://localhost:8080'
    
    return True

def start_distributed_service():
    """Start the distributed tree search service."""
    service_dir = Path('distributed-tree-search-service')
    
    if not service_dir.exists():
        print("❌ Distributed service directory not found")
        print("Make sure 'distributed-tree-search-service' directory exists")
        return False
    
    print("🚀 Starting distributed tree search service...")
    
    # Change to service directory
    os.chdir(service_dir)
    
    # Check if Docker Compose is available
    try:
        subprocess.run(['docker-compose', '--version'], 
                      capture_output=True, check=True)
        
        print("Using Docker Compose to start service...")
        
        # Start services
        result = subprocess.run(['docker-compose', 'up', '-d'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Service started with Docker Compose")
            return True
        else:
            print(f"❌ Failed to start service: {result.stderr}")
            return False
            
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("Docker Compose not available, trying manual start...")
        
        # Try manual start
        try:
            # Start API server
            subprocess.Popen(['python', 'app.py'])
            time.sleep(3)
            
            # Start workers
            subprocess.run(['chmod', '+x', 'scripts/start-workers.sh'])
            subprocess.run(['./scripts/start-workers.sh'])
            
            print("✅ Service started manually")
            return True
            
        except Exception as e:
            print(f"❌ Failed to start service manually: {e}")
            return False
    
    finally:
        # Change back to original directory
        os.chdir('..')

def test_service():
    """Test if the distributed service is working."""
    print("🧪 Testing distributed service...")
    
    # Wait for service to be ready
    max_attempts = 30
    for attempt in range(max_attempts):
        try:
            response = requests.get('http://localhost:8080/api/v1/health', timeout=5)
            if response.status_code == 200:
                print("✅ Service is healthy and responding")
                break
        except requests.exceptions.RequestException:
            pass
        
        if attempt < max_attempts - 1:
            print(f"Waiting for service... (attempt {attempt + 1}/{max_attempts})")
            time.sleep(2)
    else:
        print("❌ Service failed to start or is not responding")
        return False
    
    # Test with a simple search
    try:
        test_request = {
            "target_smiles": "CCO",  # Simple ethanol
            "max_routes": 5,
            "max_depth": 1
        }
        
        response = requests.post(
            'http://localhost:8080/api/v1/search',
            json=test_request,
            timeout=10
        )
        
        if response.status_code == 202:
            print("✅ Test search submitted successfully")
            return True
        else:
            print(f"❌ Test search failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Test search failed: {e}")
        return False

def show_usage():
    """Show usage instructions."""
    print("""
🎉 Distributed Tree Search Setup Complete!

Your application is now configured to use the distributed service.

Next steps:
1. The service is running at: http://localhost:8080
2. Monitor workers at: http://localhost:5555 (Flower dashboard)
3. Your existing code will automatically use distributed processing

To test in your application:
```python
from retro_runner import retro_runner

# This will now use distributed processing!
routes, message = retro_runner("CCO", request_id="test_123")
```

To scale workers:
```bash
cd distributed-tree-search-service
./scripts/scale-workers.sh 5 10  # 5 molecule workers, 10 reaction workers
```

To stop the service:
```bash
cd distributed-tree-search-service
docker-compose down  # or ./scripts/stop-workers.sh
```

Configuration:
- Edit .env file to adjust settings
- Set USE_DISTRIBUTED_SEARCH=false to use local processing
""")

def main():
    """Main setup function."""
    print("🔧 Setting up Distributed Tree Search Service")
    print("=" * 50)
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Check Redis
    if not check_redis():
        sys.exit(1)
    
    # Setup environment
    if not setup_environment():
        sys.exit(1)
    
    # Start service
    if not start_distributed_service():
        sys.exit(1)
    
    # Test service
    if not test_service():
        print("⚠️  Service started but test failed. Check logs for issues.")
    
    # Show usage
    show_usage()
    
    print("✅ Setup completed successfully!")

if __name__ == "__main__":
    main()
