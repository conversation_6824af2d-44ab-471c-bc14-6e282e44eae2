import os
from typing import Dict, Any

class Config:
    """Configuration settings for the retro-synthesis tree search."""

    #API HOSTNAMES
    AZURE_HOSTNAME = os.environ.get('AZURE_HOSTNAME', "***********")
    ASKOS_PREDICTIONS_PISTACHO_URL = f"http://{AZURE_HOSTNAME}:9420/predictions/pistachio_23Q3"
    ASKCOS_EM_URL = f"http://{AZURE_HOSTNAME}:9451/predictions"
    REACTION_CLASS_URL = f"http://{AZURE_HOSTNAME}:9621/reaction_class"
    ASKOS_PRE_REAXYS = f"http://{AZURE_HOSTNAME}:9410/predictions/reaxys"

    # single step retro parameters
    SINGLE_STEP_CONFIG = "config/singlestep_config.yaml"
    PRE_LOADED_RETRO_MODELS = 0

    AZURE_BLOB_DISCONNECTIONS_BASE_PATH = 'molecules/'
    # Tree search parameters
    MAX_DEPTH = int(os.getenv('MAX_DEPTH', '2'))
    BUILDING_BLOCKS_PATH = "assets/stocks/mstack_buildingblocks.json"
    SYNTHESIS_SCORE_THRESHOLD = float(os.getenv('SYNTHESIS_SCORE_THRESHOLD', '1.05'))
    MIN_FORWARD_PROB = float(os.getenv('MIN_FORWARD_PROB', '0.7'))
    MIN_CERTAINITY_SCORE = float(os.getenv('MIN_CERTAINITY_SCORE', '0.7'))
    HEAVY_METAL_THRESHOLD = float(os.getenv('HEAVY_METAL_THRESHOLD', '31.0'))


    # Configs for parallel runs
    PARALLEL_CONFIGS = os.getenv('PARALLEL_CONFIGS', '''[{"MAX_DEPTH": 1, "BEAM_WIDTH": 100, "DELAY" : 0},{"MAX_DEPTH": 2, "BEAM_WIDTH": 50, "DELAY" : 1500}]''')
    # Search parameters
    BEAM_WIDTH = int(os.getenv('BEAM_WIDTH', '4'))
    PRUNING_FACTOR = float(os.getenv('PRUNING_FACTOR', '0.05')) #heavy pruning for to pick full routes at max depth
    BEAM_BASED_PRUNING = int(os.getenv('BEAM_BASED_PRUNING', '1'))
    MAX_ROUTES = int(os.getenv('MAX_ROUTES', '100'))
    
    # Redis/Celery settings
    USE_CELERY = os.getenv('USE_CELERY', 'False').lower() == 'true'
    REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    CELERY_BROKER_URL = os.getenv('CELERY_BROKER_URL', REDIS_URL)
    CELERY_RESULT_BACKEND = os.getenv('CELERY_RESULT_BACKEND', REDIS_URL)

    # Distributed Tree Search Settings
    USE_DISTRIBUTED_SEARCH = os.getenv('USE_DISTRIBUTED_SEARCH', 'False').lower() == 'true'
    DISTRIBUTED_MOLECULE_QUEUE = os.getenv('DISTRIBUTED_MOLECULE_QUEUE', 'molecule_expansion_queue')
    DISTRIBUTED_REACTION_QUEUE = os.getenv('DISTRIBUTED_REACTION_QUEUE', 'reaction_processing_queue')
    DISTRIBUTED_RESULTS_QUEUE = os.getenv('DISTRIBUTED_RESULTS_QUEUE', 'results_aggregation_queue')

    # Worker Configuration
    MAX_MOLECULE_WORKERS = int(os.getenv('MAX_MOLECULE_WORKERS', '10'))
    MAX_REACTION_WORKERS = int(os.getenv('MAX_REACTION_WORKERS', '20'))
    WORKER_CONCURRENCY = int(os.getenv('WORKER_CONCURRENCY', '4'))

    # Queue Management
    QUEUE_BATCH_SIZE = int(os.getenv('QUEUE_BATCH_SIZE', '50'))
    MAX_QUEUE_SIZE = int(os.getenv('MAX_QUEUE_SIZE', '10000'))
    QUEUE_TIMEOUT = int(os.getenv('QUEUE_TIMEOUT', '300'))  # 5 minutes

    # Distributed Processing Timeouts
    MOLECULE_TASK_TIMEOUT = int(os.getenv('MOLECULE_TASK_TIMEOUT', '600'))  # 10 minutes
    REACTION_TASK_TIMEOUT = int(os.getenv('REACTION_TASK_TIMEOUT', '300'))  # 5 minutes
    COORDINATION_TASK_TIMEOUT = int(os.getenv('COORDINATION_TASK_TIMEOUT', '3600'))  # 1 hour

    # Checkpointing settings
    # Value is set when the class is initialised once, for updation during parallel exec, use method
    CHECKPOINT_FREQUENCY = int(os.getenv('CHECKPOINT_FREQUENCY', str((MAX_DEPTH + 1) * 100)))
    RETRO_INPUT_REDIS_QUEUE = os.getenv('RETRO_INPUT_REDIS_QUEUE', 'retro_input_job_queue')
    RETRO_OUTPUT_REDIS_QUEUE = os.getenv('RETRO_OUTPUT_REDIS_QUEUE', 'retro_output_job_queue')
    ENRICH_INPUT_REDIS_QUEUE = os.getenv('ENRICH_INPUT_REDIS_QUEUE', 'enrich_input_job_queue')
    ENRICH_OUTPUT_REDIS_QUEUE = os.getenv('ENRICH_OUTPUT_REDIS_QUEUE', 'enrich_output_job_queue')

    AZURE_BLOB_CONNECTION_STRING = os.getenv('AZURE_BLOB_CONNECTION_STRING', '')
    AZURE_BLOB_CONTAINER_NAME = os.getenv('AZURE_BLOB_CONTAINER_NAME', 'imagestorage')

    REDIS_SENTINEL_URL = os.getenv('REDIS_SENTINEL_URL') # redis://localhost:26379
    REDIS_SENTINEL_SERVICE_NAME = os.getenv('REDIS_SENTINEL_SERVICE_NAME', 'mymaster')
