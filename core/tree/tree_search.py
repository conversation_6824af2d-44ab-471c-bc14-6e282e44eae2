import pandas as pd
from typing import List, Optional, Tuple
from collections import deque
import logging, traceback
import datetime
import time
import os
from config.settings import Config
from api.synthesis_score_api import *
from core.tree.tree_nodes import *
from core.tree.scoring import *
from core.tree.tree_search import *
from core.validators.disconnection_validator import *
from core.validators.terminal_checker import *
from db.db_ops import DbOps
from azure_utils.utils import AzureUtils
from utils.common import *
from utils.helpers import REQUEST_ID, canonicalize_smiles
from rdkit import Chem
from concurrent.futures import ThreadPoolExecutor, as_completed
from celery.utils.log import get_task_logger
from enrich_inflow_worker import send_enrich_inflow_task


logger = get_task_logger("tasks")

class RetroSynthesisTreeSearch:
    """Main tree search algorithm for multi-step retro-synthesis."""
    
    def __init__(
        self,
        single_step_api,
        terminal_checker: <PERSON><PERSON><PERSON><PERSON>,
        disconnection_validator: Disconnection<PERSON><PERSON><PERSON><PERSON>,
        route_scorer: RouteScorer,
        synthesis_score_api: SynthesisScoreAPI,
        config: Config
    ):
        self.single_step_api = single_step_api
        self.terminal_checker = terminal_checker
        self.disconnection_validator = disconnection_validator
        self.route_scorer = route_scorer
        self.synthesis_score_api = synthesis_score_api
        self.config = config
        self.db_ops = DbOps()
        self.azure_utils = AzureUtils()

        
    def search(self, target_smiles: str, max_routes: int = None, kwargs : dict = {}):
        """
        Perform tree search to find best synthesis routes.
        
        Args:
            target_smiles: Target molecule SMILES
            max_routes: Maximum number of routes to return
            
        Returns:
            List of synthesis routes (each route is a list of ReactionNodes)
        """
        if max_routes is None:
            max_routes = self.config.MAX_ROUTES
            
        # Create root node
        root = MoleculeNode(smiles=target_smiles, depth=0)
        #print(root)

        # Check if root is already terminal
        if self.terminal_checker.is_terminal(root):
            root.status = NodeStatus.TERMINAL
            print("root is terminal")
            return [], "The molecule is a building block, eliminating the need for retrosynthesis."
        
        # Perform breadth-first search
        #print("root is not terminal - expanding")
        #self._expand_tree_old(root)
        self._expand_tree(root, kwargs)
        
        # Extract and score routes
        routes = self._extract_routes(root)
        scored_routes = [(route, self.route_scorer.score_route(route)) for route in routes]

        # Sort by score and return top routes
        #sorts in ascending order by default based on the second element of each item (i.e., x[1])
        scored_routes.sort(key=lambda x: x[1])
        logger.info(f"Found {len(scored_routes)} routes for target {target_smiles}")
        return [route for route, _ in scored_routes[:max_routes]], ''

    def _check_molecule_in_ancestry_full(self, mol_node: MoleculeNode, new_smiles: str) -> bool:
        """
        Check if a molecule SMILES already exists in the ancestry path.
        
        Args:
            mol_node: Current molecule node
            new_smiles: SMILES to check for round trip
            
        Returns:
            bool: True if round trip detected
        """
        current = mol_node
        
        while current is not None:
            if isinstance(current, MoleculeNode) and canonicalize_smiles(current.smiles) == canonicalize_smiles(new_smiles):
                return True
            current = current.parent
        
        return False

    def _check_molecule_in_ancestry(self, mol_node: MoleculeNode, new_smiles: str) -> bool:
        """
        Check if a molecule SMILES already exists in the ancestry path up to grandparent.
        
        Args:
            mol_node: Current molecule node
            new_smiles: SMILES to check for round trip
            
        Returns:
            bool: True if round trip detected
        """
        current = mol_node
        generations_checked = 0
        max_generations = 2  # parent (1) + grandparent (2)
        
        while current is not None and generations_checked < max_generations:
            if isinstance(current, MoleculeNode) and current.smiles == new_smiles:
                return True
            current = current.parent
            generations_checked += 1
        
        return False
  
    def _apply_pruning_factor(self, nodes: List[TreeNode], pruning_factor: float = 0.9) -> List[TreeNode]:
        """
        Apply pruning to keep only the top percentage of nodes based on their scores.
        
        Args:
            nodes: List of nodes to prune
            pruning_factor: Fraction of nodes to keep (e.g., 0.9 = top 90%)
            
        Returns:
            Pruned list of top nodes
        """
        if len(nodes) <= 1:
            return nodes
        
        # Calculate how many nodes to keep
        keep_count = max(1, int(len(nodes) * pruning_factor))
        
        # Score each node (same scoring logic as before)
        scored_nodes = []
        for node in nodes:
            if isinstance(node, MoleculeNode):
                if node.synthesis_score is None:
                    node.synthesis_score = self.synthesis_score_api.get_synthesis_score(node.smiles)
                score = node.synthesis_score
            elif isinstance(node, ReactionNode):
                #forward_prob = node.reaction_data.get('Prob_Forward_Prediction_1', 0.0)
                #score = -forward_prob
                score = -node.reaction_score
            else:
                score = float('inf')
            
            scored_nodes.append((node, score))
        
        # Sort by score and take top percentage
        scored_nodes.sort(key=lambda x: x[1])
        return [node for node, _ in scored_nodes[:keep_count]]

    def _apply_beam_pruning(self, nodes: List[TreeNode], beam_width: int) -> List[TreeNode]:
        """
        Apply beam pruning to keep only the top-k nodes based on their scores.
        
        Args:
            nodes: List of nodes to prune
            beam_width: Maximum number of nodes to keep
            
        Returns:
            Pruned list of top nodes
        """
        if len(nodes) <= beam_width:
            return nodes
        
        # Score each node
        scored_nodes = []
        for node in nodes:
            if isinstance(node, MoleculeNode):
                # For molecule nodes, use synthesis score (lower is better)
                if node.synthesis_score is None:
                    node.synthesis_score = self.synthesis_score_api.get_synthesis_score(node.smiles)
                score = node.synthesis_score
            elif isinstance(node, ReactionNode):
                # For reaction nodes, use forward prediction probability (higher is better)
                use_prob_score = 1
                if use_prob_score ==1:
                    #forward_prob = node.reaction_data.get('Prob_Forward_Prediction_1', 0.0)
                    #score = -forward_prob
                    score = -node.reaction_score
                else:
                    #pick easier molecules 
                    max_reactant_score = 0.0       
                    #for reactant_node in node.get_reactant_nodes():
                    for reactants_smiles in self._parse_reactants(node.reaction_data) :
                        node_synthesis_score = self.synthesis_score_api.get_synthesis_score(reactants_smiles)
                        #print(node_synthesis_score)
                        if node_synthesis_score > max_reactant_score:
                            max_reactant_score = node_synthesis_score                           

                    score = max_reactant_score
                    #print(f"max_reactant_score is {max_reactant_score}")

            else:
                score = float('inf')  # Fallback for unknown node types
            
            scored_nodes.append((node, score))
        
        # Sort by score (ascending - lower is better) and take top beam_width
        scored_nodes.sort(key=lambda x: x[1])
        # print("************************")
        # print([score for _, score in scored_nodes[:10]])
        # print("************************")
        # exit(1)
        return [node for node, _ in scored_nodes[:beam_width]]

    def _expand_tree_old(self, root: MoleculeNode) -> None:
        """Expand the search tree using BFS."""
        queue = deque([root])
        
        while queue:
            current_node = queue.popleft()
            
            if isinstance(current_node, MoleculeNode):
                self._expand_molecule_node(current_node, queue)
            elif isinstance(current_node, ReactionNode):
                self._expand_reaction_node(current_node, queue)
    
    def _expand_tree(self, root: MoleculeNode, kwargs : None) -> None:
        """Expand the search tree using BFS."""
        queue = deque([root])
        iteration_count = 0
        if kwargs is None:
            kwargs = {}
        beam_width = kwargs.get('beam_width', self.config.BEAM_WIDTH)
        checkpoint_frequency = kwargs.get('checkpoint_frequency', self.config.CHECKPOINT_FREQUENCY)
        # checkpoint_frequency = getattr(self.config, 'CHECKPOINT_FREQUENCY', 100)
        print(f"self.config.BEAM_BASED_PRUNING = {self.config.BEAM_BASED_PRUNING}")
        print(f"self.config.BEAM_WIDTH = {beam_width}")
        print(f"self.config.PRUNING_FACTOR = {self.config.PRUNING_FACTOR}")
        print(f"queue size : {len(queue)}")
        while queue:
            current_node = queue.popleft()
            iteration_count += 1
            
            if isinstance(current_node, MoleculeNode):
                self._expand_molecule_node(current_node, queue) #add to queue after pruning
                if self.config.BEAM_BASED_PRUNING == 1:
                    if len(current_node.children) > beam_width:
                        print(f"{len(current_node.children)} dsiconnections - About to apply beam pruning to top {beam_width} disconnections")
                        current_node.children = self._apply_beam_pruning(
                            current_node.children, 
                            beam_width
                        )
                else:
                    if len(current_node.children) > 1:
                        current_node.children = self._apply_pruning_factor(
                            current_node.children, 
                            self.config.PRUNING_FACTOR
                        )
                # THEN add only the pruned children to queue
                for child in current_node.children:
                    queue.append(child)
                print(f"Appended new reactions; len of queue: {len(queue)}")

               #exit(1)
            elif isinstance(current_node, ReactionNode):
                #self._expand_reaction_node(current_node, queue)
                self._expand_reaction_node_no_roundtrip(current_node, queue)
            
            # Create checkpoint at specified frequency
            if self._should_checkpoint(iteration_count, checkpoint_frequency):
                # Extract and score routes
                routes = self._extract_routes(root)
                logger.info(f"Checkpoint at iteration {iteration_count}: Found {len(routes)} routes")
                scored_routes = [(route, self.route_scorer.score_route(route)) for route in routes]
                self._store_partial_pathways(
                    target_smiles=root.smiles, 
                    routes=scored_routes,
                    kwargs=kwargs)
                # checkpoint_id = self._create_checkpoint(root, scored_routes)
                logger.info(f"Stored partial pathways at iteration {iteration_count} with {len(routes)} routes")
                
                # checkpoint_id = self._create_checkpoint(root, scored_routes)
        print(f"************** final queue size : {len(queue)}")
        return
    
    def _expand_tree_beam(self, root: MoleculeNode) -> None:
        """Expand the search tree using BFS."""
        queue = deque([root])
        
        while queue:
            current_node = queue.popleft()
            
            if isinstance(current_node, MoleculeNode):
                self._expand_molecule_node(current_node, queue)
                # Apply beam pruning ONLY to reaction children (OR node)
                if len(current_node.children) > self.config.BEAM_WIDTH:
                    current_node.children = self._apply_beam_pruning(
                        current_node.children, 
                        self.config.BEAM_WIDTH
                    )
            elif isinstance(current_node, ReactionNode):
                self._expand_reaction_node(current_node, queue)
                # NO beam pruning for precursor molecules (AND node)
                # All precursors must be kept for the reaction to be valid
    
    def _expand_molecule_node(self, mol_node: MoleculeNode, queue: deque) -> None:
        """Expand a molecule node by finding possible reactions."""
        # Check if already terminal
        if self.terminal_checker.is_terminal(mol_node):
            print("hit a terminal node")
            mol_node.status = NodeStatus.TERMINAL
            return
        
        # Get disconnections from single-step API
        try:
            disconnections_df = self.single_step_api.get_retrosynthesis_reactions(mol_node.smiles)
            
            if disconnections_df.empty:
                logger.warning(f"No disconnections found for {mol_node.smiles}")
                return
            
            # Create reaction nodes for valid disconnections
            print(f"total disconnections to begin with: {len(disconnections_df)}")
            valid_disconnections= 0
            for _, row in disconnections_df.iterrows():
                #print(row)
                if self.disconnection_validator.is_valid(row, mol_node.smiles):
                    valid_disconnections+=1
                    reaction_node = self._create_reaction_node(row, mol_node)
                    reaction_node.reaction_score = row['ltr_score_scaled']
                    #print(reaction_node.reaction_score)
                    #exit(1)
                    mol_node.add_child(reaction_node)
                    #queue.append(reaction_node)
                    #print(f"len of queue: {len(queue)}")
                #else:
                    #print(row)
                    #print("invalid disconnections")
                    #print(f"invalid disconnections: ",row['rxn_class'].get('prediction_certainty', 0.0))
                    #exit(1)
            print(f"total valid disconnections: {valid_disconnections}")
            #exit(1)
                    
        except Exception as e:
            logger.error(f"Error expanding molecule {mol_node.smiles}: {e} {traceback.format_exc()}")

    def _expand_reaction_node_no_roundtrip(self, rxn_node: ReactionNode, queue: deque) -> None:
        """
        Expand a reaction node by creating precursor molecule nodes.
        Modified version of _expand_reaction_node that prevents round trips.
        """
        try:
            # Parse reactants from the reaction data
            reactants_smiles = self._parse_reactants(rxn_node.reaction_data)
            
            # Create molecule nodes for each reactant
            for reactant_smiles in reactants_smiles:
                # Check for round trip before creating node
                if not self._check_molecule_in_ancestry_full(rxn_node.parent, reactant_smiles):
                    # reactant_node = MoleculeNode(smiles=reactant_smiles)
                    reactant_node = MoleculeNode(smiles=canonicalize_smiles(reactant_smiles))
                    if self.terminal_checker.is_terminal(reactant_node):
                        reactant_node.status = NodeStatus.TERMINAL
                        #print("reactant_node is terminal")
                        #print(f"reactant_node is terminal; Not Appending new reactant_node; len of queue: {len(queue)}")
                    else:                
                        queue.append(reactant_node)                    
                    rxn_node.add_child(reactant_node)
                else:
                    print(f"Preventing round trip: {reactant_smiles} already in ancestry")
                    # Mark reaction as invalid due to round trip
                    rxn_node.status = NodeStatus.INVALID
                    break        
        except Exception as e:
            rxn_node.status = NodeStatus.INVALID

    def _expand_reaction_node(self, rxn_node: ReactionNode, queue: deque) -> None:
        """Expand a reaction node by creating precursor molecule nodes."""
        try:
            # Parse reactants from the reaction data
            reactants_smiles = self._parse_reactants(rxn_node.reaction_data)
            
            # Create molecule nodes for each reactant
            for reactant_smiles in reactants_smiles:
                reactant_node = MoleculeNode(smiles=reactant_smiles)
                if self.terminal_checker.is_terminal(reactant_node):
                    reactant_node.status = NodeStatus.TERMINAL
                    #print("reactant_node is terminal")
                    #print(f"reactant_node is terminal; Not Appending new reactant_node; len of queue: {len(queue)}")
                else:                
                    queue.append(reactant_node)
                    #print(f"appending reaction node , len of queue: {len(queue)}")
                rxn_node.add_child(reactant_node) #
        except Exception as e:
            logger.error(f"Error expanding reaction {rxn_node.node_id}: {e}")
            rxn_node.status = NodeStatus.INVALID
    
    def _create_reaction_node(self, disconnection_row: pd.Series, parent: MoleculeNode) -> ReactionNode:
        """Create a reaction node from disconnection data."""
        reaction_data = disconnection_row.to_dict()
        reactants = self._parse_reactants(reaction_data)
        
        return ReactionNode(
            reaction_data=reaction_data,
            reactants=reactants
        )
    
    def _parse_reactants(self, reaction_data: dict) -> List[str]:
        """Parse reactant SMILES from reaction data."""
        retro_smiles = reaction_data.get('Retro', '')
        # Split by '.' to get individual reactants
        return [smiles.strip() for smiles in retro_smiles.split('.') if smiles.strip()]
    
    def _extract_routes_old(self, root: MoleculeNode) -> List[List[ReactionNode]]:
        """Extract all complete synthesis routes from the tree."""
        routes = []
        seen_routes = set()
        
        def dfs_extract(node: TreeNode, current_route: List[ReactionNode]) -> None:
            if isinstance(node, MoleculeNode):
                print(node.smiles)
                print(node.synthesis_score)
                if node.status == NodeStatus.TERMINAL:
                    print("# Found a complete route")
                    if current_route:
                        print("# Appending to route")
                        routes.append(current_route.copy())
                    # if current_route:
                    #     route_signature = tuple(r.get_id() for r in current_route)
                    #     if route_signature not in seen_routes:
                    #         seen_routes.add(route_signature)
                    #         routes.append(current_route.copy())                    
                else:
                    # Continue with solved reactions
                    print(f"# Not Found a complete route: {len(current_route)}")
                    for reaction in node.get_solved_reactions():
                        dfs_extract(reaction, current_route)
            
            elif isinstance(node, ReactionNode):
                if node.is_solved():
                    print(f"# node is solvede: {len(current_route)}")
                    current_route.append(node)
                    print(f"# node is solvede: {len(current_route)}")
                    # Process all reactants
                    for reactant in node.get_reactant_nodes():
                        dfs_extract(reactant, current_route)
                    current_route.pop()
        
        dfs_extract(root, [])
        print(len(routes))
        #exit(1)
        return routes
    
    def _store_partial_pathways(self, target_smiles: str, routes: List[Tuple[List[ReactionNode], float]], kwargs : dict = {}) -> str:
        """
        Storing Partial pathways in MongoDB for later analysis.
        Args:
            target_smiles: Target molecule SMILES
            routes: List of scored routes
        Returns:
            Checkpoint ID for later retrieval
        """
        logger.info(f"Storing partial pathways for target: {target_smiles}")
        
        # Create a unique checkpoint ID based on timestamp
        
        pathways_data = {
            'target_smiles': target_smiles,
            'timestamp': datetime.datetime.now().isoformat(),
            'readable_time': time.ctime(),
            'num_routes': len(routes),
            'routes': []
        }

        smiles_metadata = {}
        all_data = []
        for route_idx, (route, _) in enumerate(routes):
            route_data = {
                'route_id': route_idx + 1,
                'num_steps': len(route),
                'unique_id': hash(str(route)),
                'reactions': []
            }
            raw_smiles = []
            route_level_image = ''
            route_name = ''
            for step_idx, reaction in enumerate(route):
                reaction_data = {
                    'step': step_idx + 1,
                    'reaction_id': reaction.node_id,
                    'reaction_string': reaction.reaction_data.get('rxn_string', 'N/A'),
                    'retro_smiles': reaction.reaction_data.get('Retro', ''),
                    'reagents': reaction.reaction_data.get('Reagents', ''),
                    'forward_prediction': reaction.reaction_data.get('Forward_Prediction', ''),
                    'prob_forward_1': reaction.reaction_data.get('Prob_Forward_Prediction_1', 0.0),
                    'prob_forward_2': reaction.reaction_data.get('Prob_Forward_Prediction_2', 0.0),
                    'score': reaction.reaction_score,# reaction.reaction_data.get('Score', 0.0),
                    'rxn_class': reaction.reaction_data.get('rxn_class', 0.0),
                    'reaction_smiles_img' :  self.azure_utils.image_to_blob(
                        reaction.reaction_data.get('rxn_string', '')),
                    'other_information' : smiles_metadata.get(
                        reaction.reaction_data.get('rxn_string', '')),
                    'reactants': []
                }
                if not route_level_image:
                    route_level_image = reaction_data.get('reaction_smiles_img', '')
                
                if not route_name:
                    route_name = reaction_data.get('rxn_class', {}).get('reaction_name')
                # Add reactant information
                mid_materials = []
                for reactant_node in reaction.get_reactant_nodes():
                    reactant_info = {
                        'smiles': reactant_node.smiles,
                        'name' : smiles_to_iupac_pubchem(reactant_node.smiles),
                        'synthesis_score': reactant_node.synthesis_score,
                        'is_terminal': reactant_node.status.value if hasattr(reactant_node.status, 'value') else str(reactant_node.status)
                    }
                    reaction_data['reactants'].append(reactant_info)
                    mid_materials.append(reactant_info.get('name'))
                
                route_data['reactions'].append(reaction_data)
                raw_smiles = mid_materials
                
                
            
           
            total_score = 0
            for reaction in route:
                reactant_scores = [
                    reactant.synthesis_score or 0 
                    for reactant in reaction.get_reactant_nodes()
                ]
                if reactant_scores:
                    total_score += max(reactant_scores)

            route_data['total_route_score'] = total_score
            
            pathways_data['routes'].append(route_data)
            
            # Insert individual route record
        
            route_id=self.db_ops.insert_retro_data(
                target_smiles=target_smiles,
                request_id=kwargs.get('request_id'),
                unique_id=route_data['unique_id'],
                route_id=route_data['route_id'],
                num_steps=route_data['num_steps'],
                raw_smiles=raw_smiles,
                total_cost = 0,
                total_route_score=route_data['total_route_score'],
                route_name=route_name,
                data=route_data['reactions'],
                route_level_image=route_level_image
                )
            
            if route_id:
                logger.info(f"Sending enrich task for route_id: {route_id}")
                other_info = []
                for dt in route_data['reactions']:
                    other_info.append({'reaction_smiles' : dt.get('reaction_string'), 'reaction_class' : json.dumps(dt.get('rxn_class'))})
                all_data.append({"route_id": route_id, 'other_info' : other_info , 'total_score' : total_score})
            logger.info(f"Stored route {route_data['route_id']} with {route_data['num_steps']} steps and score {route_data['total_route_score']}")
        all_data = sorted(all_data, key=lambda x: x['total_score'], reverse=True)
        for dt in all_data:
            send_enrich_inflow_task({"route_id": dt['route_id'], 'other_info' : dt['other_info']})
        return pathways_data



    
    def _create_checkpoint(self, root: MoleculeNode, scored_routes, checkpoint_id: str = None) -> str:
        """
        Create a checkpoint of the current tree state.
        
        Args:
            root: Root node of the tree
            checkpoint_id: Optional custom checkpoint ID
            
        Returns:
            Checkpoint ID for later retrieval
        """
        import pickle
        import time
        import os
        
        if checkpoint_id is None:
            checkpoint_id = f"checkpoint_{int(time.time())}"
        
        checkpoint_data = {
            'timestamp': time.time(),
            'root_node': root,
            'scored_routes': scored_routes,
            'config': self.config,
            'checkpoint_id': checkpoint_id
        }
        
        # Save to file or Redis depending on configuration
        if self.config.USE_CELERY:
            # Save to Redis for distributed access
            import redis
            r = redis.from_url(self.config.REDIS_URL)
            r.set(f"checkpoint:{checkpoint_id}", pickle.dumps(checkpoint_data))
        else:
            # Save to local file
            os.makedirs("checkpoints", exist_ok=True)
            with open(f"checkpoints/{checkpoint_id}.pkl", 'wb') as f:
                pickle.dump(checkpoint_data, f)
        
        return checkpoint_id

    def _should_checkpoint(self, iteration_count: int, checkpoint_frequency: int = 100) -> bool:
        """
        Determine if a checkpoint should be created based on frequency.
        
        Args:
            iteration_count: Current iteration number
            checkpoint_frequency: How often to checkpoint
            
        Returns:
            True if checkpoint should be created
        """
        return False
        # return iteration_count % checkpoint_frequency == 0
    
    def get_best_routes_from_checkpoint(self, checkpoint_id: str, max_routes: int = None) -> List[List[ReactionNode]]:
        """
        Load checkpoint and extract best routes without continuing search.
        
        Args:
            checkpoint_id: ID of the checkpoint to load
            max_routes: Maximum number of routes to return
            
        Returns:
            List of best synthesis routes from the checkpoint
        """
        import pickle
        
        if max_routes is None:
            max_routes = self.config.MAX_ROUTES
        
        # Load checkpoint data
        if self.config.USE_CELERY:
            import redis
            r = redis.from_url(self.config.REDIS_URL)
            checkpoint_data = pickle.loads(r.get(f"checkpoint:{checkpoint_id}"))
        else:
            with open(f"checkpoints/{checkpoint_id}.pkl", 'rb') as f:
                checkpoint_data = pickle.load(f)
        
        root = checkpoint_data['root_node']
        
        # Extract and score routes from current tree state
        routes = self._extract_routes(root)
        scored_routes = [(route, self.route_scorer.score_route(route)) for route in routes]
        
        # Sort by score and return top routes
        scored_routes.sort(key=lambda x: x[1])
        return [route for route, _ in scored_routes[:max_routes]]
    
    def _extract_routes(self, root: MoleculeNode) -> List[Dict]:
        """
        Extract all complete synthesis routes from the tree.
        Each route contains the hierarchical structure with all sub-trees.
        
        Returns:
            List of route dictionaries, where each route represents a complete synthesis pathway
        """
        routes = []
        
        def build_route_tree(node: TreeNode) -> Dict:
            """
            Recursively build the route tree structure.
            
            Returns:
                Dictionary representing the node and its subtree
            """
            if isinstance(node, MoleculeNode):
                node_dict = {
                    'type': 'molecule',
                    'smiles': node.smiles,
                    'node_id': node.node_id,
                    'depth': node.depth,
                    'status': node.status.value,
                    'synthesis_score': node.synthesis_score,
                    'reactions': []
                }
                
                if node.status == NodeStatus.TERMINAL:
                    # Terminal node - no further reactions needed
                    return node_dict
                
                # Get all solved reactions for this molecule
                solved_reactions = node.get_solved_reactions()
                if not solved_reactions:
                    # No solved reactions - this pathway is incomplete
                    return None
                
                # For OR nodes (molecules), we need to choose one solved reaction
                # We'll create separate routes for each solved reaction
                for reaction in solved_reactions:
                    reaction_subtree = build_route_tree(reaction)
                    if reaction_subtree is not None:
                        node_dict['reactions'].append(reaction_subtree)
                
                return node_dict if node_dict['reactions'] else None
                
            elif isinstance(node, ReactionNode):
                if not node.is_solved():
                    return None
                    
                node_dict = {
                    'type': 'reaction',
                    'node_id': node.node_id,
                    'depth': node.depth,
                    'reaction_data': node.reaction_data,
                    'reactants': node.reactants,
                    'reaction_score': getattr(node, 'reaction_score', None),
                    'precursors': []
                }
                
                # For AND nodes (reactions), all precursors must be solved
                reactant_nodes = node.get_reactant_nodes()
                for reactant_node in reactant_nodes:
                    precursor_subtree = build_route_tree(reactant_node)
                    if precursor_subtree is None:
                        # If any precursor is not solved, this reaction path is invalid
                        return None
                    node_dict['precursors'].append(precursor_subtree)
                
                return node_dict
            
            return None
        
        def extract_all_complete_routes(node_dict: Dict, current_path: List = None) -> List[List]:
            """
            Extract all possible complete routes from the hierarchical structure.
            This handles the OR logic where a molecule can be synthesized via multiple reactions.
            
            Returns:
                List of complete synthesis routes (each route is a flat list of reactions)
            """
            if current_path is None:
                current_path = []
            
            complete_routes = []
            
            if node_dict['type'] == 'molecule':
                if node_dict['status'] == 'TERMINAL':
                    # Base case: terminal molecule
                    return [current_path] if current_path else [[]]
                
                # For each possible reaction (OR logic)
                for reaction_dict in node_dict['reactions']:
                    reaction_routes = extract_all_complete_routes(reaction_dict, current_path)
                    complete_routes.extend(reaction_routes)
                    
            elif node_dict['type'] == 'reaction':
                # Add this reaction to the current path
                new_path = current_path + [node_dict]
                
                if not node_dict['precursors']:
                    # No precursors - this shouldn't happen for valid reactions
                    return [new_path]
                
                # For reactions with multiple precursors (AND logic)
                # We need to get the cartesian product of all precursor routes
                precursor_route_sets = []
                for precursor_dict in node_dict['precursors']:
                    precursor_routes = extract_all_complete_routes(precursor_dict, [])
                    precursor_route_sets.append(precursor_routes)
                
                # Generate all combinations
                if precursor_route_sets:
                    from itertools import product
                    for route_combination in product(*precursor_route_sets):
                        # Flatten the combination and add to the current path
                        combined_route = new_path[:]
                        for route in route_combination:
                            combined_route.extend(route)
                        complete_routes.append(combined_route)
                else:
                    complete_routes.append(new_path)
            
            return complete_routes
        
        # Build the complete tree structure
        root_tree = build_route_tree(root)
        
        if root_tree is None:
            logger.info("No complete routes found - tree has no solved pathways")
            return []
        
        # Extract all possible linear routes for scoring
        linear_routes = extract_all_complete_routes(root_tree)
        print(len(linear_routes))
        #exit(1)
        
        # Convert back to the expected format (List[List[ReactionNode]])
        # and also return the hierarchical structure for reference
        final_routes = []
        
        for linear_route in linear_routes:
            route_reactions = []
            for reaction_dict in linear_route:
                # Find the actual ReactionNode object
                reaction_node = self._find_reaction_node_by_id(root, reaction_dict['node_id'])
                if reaction_node:
                    route_reactions.append(reaction_node)
            
            if route_reactions:
                final_routes.append(route_reactions)
        
        logger.info(f"Extracted {len(final_routes)} complete synthesis routes")
        return final_routes

    def _find_reaction_node_by_id(self, root: MoleculeNode, node_id: str) -> Optional[ReactionNode]:
        """
        Find a ReactionNode by its ID in the tree.
        
        Args:
            root: Root node to search from
            node_id: ID of the node to find
            
        Returns:
            ReactionNode if found, None otherwise
        """
        def dfs_search(node: TreeNode) -> Optional[ReactionNode]:
            if isinstance(node, ReactionNode) and node.node_id == node_id:
                return node
            
            for child in node.children:
                result = dfs_search(child)
                if result:
                    return result
            return None
        
        return dfs_search(root)

    def get_route_hierarchy(self, root: MoleculeNode) -> List[Dict]:
        """
        Get the complete hierarchical structure of all synthesis routes.
        This preserves the tree structure for visualization or detailed analysis.
        
        Returns:
            List of hierarchical route dictionaries
        """
        routes = []
        
        def build_complete_hierarchy(node: TreeNode) -> Dict:
            """Build the complete hierarchical structure preserving the tree."""
            if isinstance(node, MoleculeNode):
                node_dict = {
                    'type': 'molecule',
                    'smiles': node.smiles,
                    'node_id': node.node_id,
                    'depth': node.depth,
                    'status': node.status.value,
                    'synthesis_score': node.synthesis_score,
                    'reactions': []
                }
                
                # Include all reactions (solved and unsolved) for complete picture
                for child in node.children:
                    if isinstance(child, ReactionNode):
                        reaction_dict = build_complete_hierarchy(child)
                        if reaction_dict:
                            node_dict['reactions'].append(reaction_dict)
                
                return node_dict
                
            elif isinstance(node, ReactionNode):
                node_dict = {
                    'type': 'reaction',
                    'node_id': node.node_id,
                    'depth': node.depth,
                    'status': node.status.value,
                    'is_solved': node.is_solved(),
                    'reaction_data': node.reaction_data,
                    'reactants': node.reactants,
                    'reaction_score': getattr(node, 'reaction_score', None),
                    'precursors': []
                }
                
                # Include all precursors
                for child in node.children:
                    if isinstance(child, MoleculeNode):
                        precursor_dict = build_complete_hierarchy(child)
                        if precursor_dict:
                            node_dict['precursors'].append(precursor_dict)
                
                return node_dict
            
            return None
        
        root_hierarchy = build_complete_hierarchy(root)
        return [root_hierarchy] if root_hierarchy else []