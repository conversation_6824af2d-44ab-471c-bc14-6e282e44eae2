#!/usr/bin/env python3
"""
Setup and test the distributed tree search service.
"""

import os
import sys
import subprocess
import time
import requests

def check_docker():
    """Check if Docker is available."""
    try:
        subprocess.run(['docker', '--version'], capture_output=True, check=True)
        subprocess.run(['docker-compose', '--version'], capture_output=True, check=True)
        print("✅ Docker and Docker Compose are available")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Docker or Docker Compose not found")
        print("Please install Docker Desktop: https://www.docker.com/products/docker-desktop")
        return False

def setup_environment():
    """Setup environment variables."""
    print("🔧 Setting up environment...")
    
    # Create .env file for distributed service
    env_content = """# Distributed Tree Search Configuration
USE_DISTRIBUTED_SEARCH=true
DISTRIBUTED_SEARCH_URL=http://localhost:8080

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Worker Configuration
MAX_MOLECULE_WORKERS=2
MAX_REACTION_WORKERS=3
MOLECULE_WORKER_CONCURRENCY=2
REACTION_WORKER_CONCURRENCY=4

# Processing Configuration
MAX_DEPTH=3
BEAM_WIDTH=50
PRUNING_FACTOR=0.1
MAX_ROUTES=100

# API Configuration
AZURE_HOSTNAME=askcos-stg.chemstack.ai
"""
    
    # Write to distributed service .env
    service_env_path = "distributed-tree-search-service/.env"
    with open(service_env_path, 'w') as f:
        f.write(env_content)
    
    # Set environment variables for main app
    os.environ['USE_DISTRIBUTED_SEARCH'] = 'true'
    os.environ['DISTRIBUTED_SEARCH_URL'] = 'http://localhost:8080'
    
    print("✅ Environment configured")
    return True

def start_service():
    """Start the distributed service."""
    print("🚀 Starting distributed service...")
    
    try:
        # Change to service directory
        os.chdir('distributed-tree-search-service')
        
        # Start with Docker Compose
        result = subprocess.run(
            ['docker-compose', 'up', '-d'],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("✅ Service started successfully")
            
            # Wait for service to be ready
            print("⏳ Waiting for service to be ready...")
            max_attempts = 30
            for attempt in range(max_attempts):
                try:
                    response = requests.get('http://localhost:8080/api/v1/health', timeout=5)
                    if response.status_code == 200:
                        print("✅ Service is ready!")
                        return True
                except:
                    pass
                
                if attempt < max_attempts - 1:
                    time.sleep(2)
            
            print("⚠️  Service started but may not be fully ready")
            return True
            
        else:
            print(f"❌ Failed to start service: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error starting service: {e}")
        return False
    finally:
        # Change back to original directory
        os.chdir('..')

def run_tests():
    """Run the test suite."""
    print("🧪 Running tests...")
    
    try:
        result = subprocess.run([sys.executable, 'test_distributed_service.py'])
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False

def show_usage():
    """Show usage instructions."""
    print("""
🎉 Setup Complete!

Your distributed tree search service is now running.

Next steps:
1. Test with your existing code:
   ```python
   from retro_runner import retro_runner
   routes, message = retro_runner("CCO", request_id="test")
   ```

2. Monitor the service:
   - API: http://localhost:8080/api/v1/health
   - Flower (workers): http://localhost:5555

3. Scale workers if needed:
   ```bash
   cd distributed-tree-search-service
   docker-compose up -d --scale molecule-worker=5 --scale reaction-worker=8
   ```

4. View logs:
   ```bash
   cd distributed-tree-search-service
   docker-compose logs -f
   ```

5. Stop the service:
   ```bash
   cd distributed-tree-search-service
   docker-compose down
   ```

To disable distributed search and use local processing:
   export USE_DISTRIBUTED_SEARCH=false
""")

def main():
    """Main setup function."""
    print("🔧 Distributed Tree Search Service Setup")
    print("=" * 50)
    
    steps = [
        ("Check Docker", check_docker),
        ("Setup Environment", setup_environment),
        ("Start Service", start_service),
        ("Run Tests", run_tests),
    ]
    
    for step_name, step_func in steps:
        print(f"\n{step_name}:")
        if not step_func():
            print(f"❌ Setup failed at: {step_name}")
            return 1
    
    show_usage()
    print("✅ Setup completed successfully!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
