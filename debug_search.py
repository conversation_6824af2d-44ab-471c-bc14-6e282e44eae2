#!/usr/bin/env python3
"""
Debug script to check what's happening with the tree search.
"""

import os
import sys
import requests
from config.settings import Config

def check_configuration():
    """Check current configuration."""
    print("🔧 Checking Configuration...")
    
    config = Config()
    
    print(f"USE_DISTRIBUTED_SEARCH: {config.USE_DISTRIBUTED_SEARCH}")
    print(f"DISTRIBUTED_SEARCH_URL: {config.DISTRIBUTED_SEARCH_URL}")
    print(f"Environment variable: {os.getenv('USE_DISTRIBUTED_SEARCH')}")
    
    return config

def test_distributed_service():
    """Test if distributed service is working."""
    print("\n🌐 Testing Distributed Service...")
    
    config = Config()
    
    try:
        # Test health endpoint
        response = requests.get(f"{config.DISTRIBUTED_SEARCH_URL}/api/v1/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Service is healthy: {data.get('status')}")
            print(f"   Redis connected: {data.get('redis_connected')}")
            print(f"   Workers active: {data.get('workers_active')}")
            return True
        else:
            print(f"❌ Service returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to distributed service: {e}")
        return False

def test_local_api():
    """Test the local single step API directly."""
    print("\n🧪 Testing Local Single Step API...")
    
    try:
        from api.singleStepRetroRelaxed_api import SingleStepRetroRelaxedAPI
        from api.synthesis_score_api import SCScoreAPI
        from config.settings import Config
        
        config = Config()
        synthesis_score_api = SCScoreAPI()
        
        # Create the API instance
        single_step_api = SingleStepRetroRelaxedAPI(None, None, None, None)
        
        # Test with your complex molecule
        target_smiles = "O=C(O)C1=CC=C(C(=C1Cl)COCC(F)(F)F)S(=O)(=O)C"
        
        print(f"Testing with: {target_smiles}")
        
        # Get disconnections
        disconnections_df = single_step_api.get_retrosynthesis_reactions(target_smiles)
        
        print(f"✅ Local API returned {len(disconnections_df)} disconnections")
        
        if len(disconnections_df) > 0:
            print("Sample disconnections:")
            for i, (_, row) in enumerate(disconnections_df.head(3).iterrows()):
                print(f"  {i+1}. {row.get('Retro', 'N/A')} (score: {row.get('ltr_score_scaled', 'N/A')})")
        
        return len(disconnections_df) > 0
        
    except Exception as e:
        print(f"❌ Error testing local API: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_distributed_api():
    """Test the distributed service API directly."""
    print("\n🌐 Testing Distributed Service API...")
    
    config = Config()
    
    try:
        # Test with simple molecule first
        search_request = {
            "target_smiles": "CCO",  # Simple ethanol
            "max_routes": 5,
            "max_depth": 1,
            "beam_width": 10
        }
        
        print(f"Testing with simple molecule: {search_request['target_smiles']}")
        
        response = requests.post(
            f"{config.DISTRIBUTED_SEARCH_URL}/api/v1/search",
            json=search_request,
            timeout=30
        )
        
        if response.status_code == 202:
            data = response.json()
            search_id = data.get('search_id')
            print(f"✅ Search submitted with ID: {search_id}")
            return True
        else:
            print(f"❌ Search submission failed: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"❌ Error testing distributed API: {e}")
        return False

def test_tree_search_engine():
    """Test the TreeSearchEngine directly."""
    print("\n🔍 Testing TreeSearchEngine...")
    
    try:
        from treeSearchEngine import TreeSearchEngine
        from api.singleStepRetroRelaxed_api import SingleStepRetroRelaxedAPI
        from api.synthesis_score_api import SCScoreAPI
        from config.settings import Config
        
        # Create components
        single_step_api = SingleStepRetroRelaxedAPI(None, None, None, None)
        synthesis_score_api = SCScoreAPI()
        config = Config()
        
        # Create engine
        engine = TreeSearchEngine(single_step_api, synthesis_score_api, config)
        
        print(f"TreeSearchEngine created")
        print(f"Using distributed search: {config.USE_DISTRIBUTED_SEARCH}")
        print(f"Tree search type: {type(engine.tree_search).__name__}")
        
        # Test with simple molecule
        target_smiles = "CCO"
        kwargs = {"max_depth": 1, "beam_width": 5}
        
        print(f"Testing search for: {target_smiles}")
        
        routes, message = engine.find_synthesis_routes(
            target_smiles=target_smiles,
            max_routes=5,
            kwargs=kwargs
        )
        
        print(f"✅ Search completed")
        print(f"   Found {len(routes)} routes")
        print(f"   Message: {message}")
        
        return len(routes) > 0
        
    except Exception as e:
        print(f"❌ Error testing TreeSearchEngine: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all debug tests."""
    print("🔍 Debugging Tree Search Issues")
    print("=" * 50)
    
    # Check configuration
    config = check_configuration()
    
    tests = []
    
    if config.USE_DISTRIBUTED_SEARCH:
        print("\n📡 DISTRIBUTED MODE ENABLED")
        tests = [
            ("Distributed Service Health", test_distributed_service),
            ("Distributed API Test", test_distributed_api),
            ("TreeSearchEngine Test", test_tree_search_engine),
        ]
    else:
        print("\n🏠 LOCAL MODE ENABLED")
        tests = [
            ("Local API Test", test_local_api),
            ("TreeSearchEngine Test", test_tree_search_engine),
        ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} passed")
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"Results: {passed}/{len(tests)} tests passed")
    
    if passed < len(tests):
        print("\n🔧 Troubleshooting suggestions:")
        if config.USE_DISTRIBUTED_SEARCH:
            print("1. Check if distributed service is running:")
            print("   cd distributed-tree-search-service && docker-compose ps")
            print("2. Check service logs:")
            print("   cd distributed-tree-search-service && docker-compose logs -f")
            print("3. Try disabling distributed search:")
            print("   export USE_DISTRIBUTED_SEARCH=false")
        else:
            print("1. Check if your local APIs are working")
            print("2. Check if ASKCOS API is accessible")
            print("3. Try enabling distributed search:")
            print("   export USE_DISTRIBUTED_SEARCH=true")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
