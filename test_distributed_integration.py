#!/usr/bin/env python3
"""
Test script to verify distributed tree search integration.
"""

import os
import sys
import time
import requests
from config.settings import Config

def test_configuration():
    """Test if configuration is set up correctly."""
    print("🔧 Testing Configuration...")
    
    config = Config()
    
    print(f"USE_DISTRIBUTED_SEARCH: {config.USE_DISTRIBUTED_SEARCH}")
    print(f"DISTRIBUTED_SEARCH_URL: {config.DISTRIBUTED_SEARCH_URL}")
    
    if not config.USE_DISTRIBUTED_SEARCH:
        print("❌ Distributed search is not enabled")
        print("Set environment variable: export USE_DISTRIBUTED_SEARCH=true")
        return False
    
    print("✅ Configuration looks good")
    return True

def test_service_connection():
    """Test connection to distributed service."""
    print("🌐 Testing Service Connection...")
    
    config = Config()
    service_url = config.DISTRIBUTED_SEARCH_URL
    
    try:
        response = requests.get(f"{service_url}/api/v1/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Service is healthy: {data.get('status')}")
            print(f"   Redis connected: {data.get('redis_connected')}")
            print(f"   Workers active: {data.get('workers_active')}")
            return True
        else:
            print(f"❌ Service returned status {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Cannot connect to service at {service_url}")
        print("Make sure the distributed service is running:")
        print("  cd distributed-tree-search-service")
        print("  docker-compose up -d")
        return False
    except Exception as e:
        print(f"❌ Error connecting to service: {e}")
        return False

def test_tree_search_engine():
    """Test TreeSearchEngine with distributed search."""
    print("🔍 Testing TreeSearchEngine Integration...")
    
    try:
        from treeSearchEngine import TreeSearchEngine
        from api.singleStepRetroRelaxed_api import SingleStepRetroRelaxedAPI
        from api.synthesis_score_api import SCScoreAPI
        from config.settings import Config
        
        # Create components (these will be ignored by distributed search)
        single_step_api = SingleStepRetroRelaxedAPI(None, None, None, None)
        synthesis_score_api = SCScoreAPI()
        config = Config()
        
        # Create engine
        engine = TreeSearchEngine(single_step_api, synthesis_score_api, config)
        
        print("✅ TreeSearchEngine created successfully")
        print(f"   Using distributed search: {config.USE_DISTRIBUTED_SEARCH}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating TreeSearchEngine: {e}")
        return False

def test_simple_search():
    """Test a simple search request."""
    print("🧪 Testing Simple Search...")
    
    try:
        from treeSearchEngine import TreeSearchEngine
        from api.singleStepRetroRelaxed_api import SingleStepRetroRelaxedAPI
        from api.synthesis_score_api import SCScoreAPI
        from config.settings import Config
        
        # Create components
        single_step_api = SingleStepRetroRelaxedAPI(None, None, None, None)
        synthesis_score_api = SCScoreAPI()
        config = Config()
        
        # Create engine
        engine = TreeSearchEngine(single_step_api, synthesis_score_api, config)
        
        # Test with simple molecule (ethanol)
        target_smiles = "CCO"
        kwargs = {"max_depth": 1, "beam_width": 5}
        
        print(f"Searching for routes to: {target_smiles}")
        
        start_time = time.time()
        routes, message = engine.find_synthesis_routes(
            target_smiles=target_smiles,
            max_routes=5,
            kwargs=kwargs
        )
        end_time = time.time()
        
        print(f"✅ Search completed in {end_time - start_time:.2f} seconds")
        print(f"   Found {len(routes)} routes")
        print(f"   Message: {message}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during search: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🚀 Testing Distributed Tree Search Integration")
    print("=" * 50)
    
    tests = [
        test_configuration,
        test_service_connection,
        test_tree_search_engine,
        test_simple_search
    ]
    
    passed = 0
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            print()
    
    print("=" * 50)
    print(f"Results: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All tests passed! Distributed search is working correctly.")
        print("\nYou can now use your existing retro_runner code:")
        print("  from retro_runner import retro_runner")
        print("  routes, message = retro_runner('CCO', request_id='test')")
    else:
        print("❌ Some tests failed. Check the output above for issues.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
