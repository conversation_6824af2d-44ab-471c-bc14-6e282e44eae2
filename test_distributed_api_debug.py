#!/usr/bin/env python3
"""
Test the distributed service API debug endpoint.
"""

import requests
import json

def test_distributed_api():
    """Test the distributed service's SingleStepAPI debug endpoint."""
    print("🧪 Testing Distributed Service SingleStepAPI...")
    
    base_url = "http://localhost:8080/api/v1"
    
    test_molecules = [
        "CCO",  # Simple ethanol
        "CC(=O)O",  # Acetic acid
        "O=C(O)C1=CC=C(C(=C1Cl)COCC(F)(F)F)S(=O)(=O)C"  # Complex molecule
    ]
    
    for smiles in test_molecules:
        print(f"\n🔍 Testing: {smiles}")
        
        try:
            response = requests.post(
                f"{base_url}/debug/test-api",
                json={"smiles": smiles},
                timeout=30
            )
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ API call successful")
                print(f"   Number of disconnections: {data.get('num_disconnections', 0)}")
                print(f"   Columns: {data.get('columns', [])}")
                
                if data.get('disconnections'):
                    print("   Sample disconnections:")
                    for i, disc in enumerate(data['disconnections'][:3]):
                        retro = disc.get('Retro', 'N/A')
                        score = disc.get('ltr_score_scaled', 'N/A')
                        print(f"     {i+1}. {retro} (score: {score})")
                else:
                    print("   ❌ No disconnections found")
                    
            else:
                print(f"❌ API call failed: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   Error: {error_data.get('message', 'Unknown error')}")
                    if 'traceback' in error_data:
                        print(f"   Traceback: {error_data['traceback']}")
                except:
                    print(f"   Response: {response.text}")
                    
        except Exception as e:
            print(f"❌ Error calling API: {e}")

def main():
    """Run the test."""
    print("🔍 Testing Distributed Service API Debug Endpoint")
    print("=" * 60)
    
    test_distributed_api()

if __name__ == "__main__":
    main()
