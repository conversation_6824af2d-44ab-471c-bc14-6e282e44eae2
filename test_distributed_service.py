#!/usr/bin/env python3
"""
Test script to verify the distributed service is working.
"""

import os
import sys
import time
import requests
import json

def test_service_health():
    """Test if the distributed service is healthy."""
    print("🔍 Testing service health...")
    
    try:
        response = requests.get("http://localhost:8080/api/v1/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Service is healthy")
            print(f"   Status: {data.get('status')}")
            print(f"   Redis connected: {data.get('redis_connected')}")
            print(f"   Workers active: {data.get('workers_active')}")
            return True
        else:
            print(f"❌ Service returned status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to service. Make sure it's running:")
        print("   cd distributed-tree-search-service")
        print("   docker-compose up -d")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_simple_search():
    """Test a simple search request."""
    print("🧪 Testing simple search...")
    
    try:
        # Submit search request
        search_request = {
            "target_smiles": "CCO",  # Simple ethanol
            "max_routes": 5,
            "max_depth": 1,
            "beam_width": 10
        }
        
        print(f"Submitting search for: {search_request['target_smiles']}")
        
        response = requests.post(
            "http://localhost:8080/api/v1/search",
            json=search_request,
            timeout=30
        )
        
        if response.status_code != 202:
            print(f"❌ Search submission failed: {response.status_code}")
            print(response.text)
            return False
        
        data = response.json()
        search_id = data.get('search_id')
        print(f"✅ Search submitted with ID: {search_id}")
        
        # Wait for completion
        max_wait = 60  # 1 minute
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            # Check status
            status_response = requests.get(
                f"http://localhost:8080/api/v1/search/{search_id}/status",
                timeout=10
            )
            
            if status_response.status_code == 200:
                status_data = status_response.json()
                current_status = status_data.get('status')
                
                print(f"   Status: {current_status}")
                
                if current_status == 'completed':
                    # Get results
                    results_response = requests.get(
                        f"http://localhost:8080/api/v1/search/{search_id}/results",
                        timeout=10
                    )
                    
                    if results_response.status_code == 200:
                        results_data = results_response.json()
                        num_routes = results_data.get('num_routes', 0)
                        print(f"✅ Search completed successfully!")
                        print(f"   Found {num_routes} routes")
                        return True
                    else:
                        print(f"❌ Failed to get results: {results_response.status_code}")
                        return False
                
                elif current_status == 'failed':
                    error_msg = status_data.get('error_message', 'Unknown error')
                    print(f"❌ Search failed: {error_msg}")
                    return False
                
                elif current_status in ['pending', 'running']:
                    progress = status_data.get('progress', {})
                    active_tasks = progress.get('active_tasks', 0)
                    print(f"   Progress: {active_tasks} active tasks")
                    time.sleep(5)
                    continue
                
            else:
                print(f"❌ Failed to get status: {status_response.status_code}")
                return False
        
        print("❌ Search timed out")
        return False
        
    except Exception as e:
        print(f"❌ Error during search: {e}")
        return False

def test_queue_stats():
    """Test queue statistics endpoint."""
    print("📊 Testing queue statistics...")
    
    try:
        response = requests.get("http://localhost:8080/api/v1/queues/stats", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ Queue stats retrieved:")
            for queue_name, stats in data.items():
                print(f"   {queue_name}: {stats.get('size', 0)} items")
            return True
        else:
            print(f"❌ Failed to get queue stats: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error getting queue stats: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Testing Distributed Tree Search Service")
    print("=" * 50)
    
    tests = [
        ("Service Health", test_service_health),
        ("Queue Statistics", test_queue_stats),
        ("Simple Search", test_simple_search),
    ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} passed")
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"Results: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All tests passed! The distributed service is working correctly.")
        print("\nYou can now enable distributed search in your main application:")
        print("  export USE_DISTRIBUTED_SEARCH=true")
        print("  python your_main_script.py")
    else:
        print("❌ Some tests failed. Check the service logs:")
        print("  docker-compose logs -f")
    
    return 0 if passed == len(tests) else 1

if __name__ == "__main__":
    sys.exit(main())
