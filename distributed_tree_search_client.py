"""
Client for the distributed tree search service.
This replaces the local tree search with calls to the distributed service.
"""

import requests
import time
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class SearchStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class DistributedSearchConfig:
    """Configuration for distributed search client."""
    service_url: str = "http://localhost:8080"
    timeout: int = 30
    poll_interval: int = 5
    max_wait_time: int = 3600  # 1 hour

class DistributedTreeSearchClient:
    """Client for distributed tree search service."""
    
    def __init__(self, config: DistributedSearchConfig = None):
        self.config = config or DistributedSearchConfig()
        self.base_url = f"{self.config.service_url}/api/v1"
        self.session = requests.Session()
        self.session.timeout = self.config.timeout
    
    def search(self, target_smiles: str, max_routes: int = None, kwargs: dict = None) -> Tuple[List[Dict], str]:
        """
        Perform distributed tree search.
        
        Args:
            target_smiles: Target molecule SMILES
            max_routes: Maximum number of routes to return
            kwargs: Additional search parameters
            
        Returns:
            Tuple of (routes, error_message)
        """
        try:
            logger.info(f"Starting distributed search for: {target_smiles}")
            
            # Prepare search request
            search_request = {
                "target_smiles": target_smiles,
                "max_routes": max_routes or 100,
                "max_depth": kwargs.get('max_depth', 3) if kwargs else 3,
                "beam_width": kwargs.get('beam_width', 50) if kwargs else 50,
                "pruning_factor": kwargs.get('pruning_factor', 0.1) if kwargs else 0.1,
                "molecule_name": kwargs.get('molecule_name') if kwargs else None,
                "request_id": kwargs.get('request_id') if kwargs else None
            }
            
            # Submit search
            search_id = self._submit_search(search_request)
            if not search_id:
                return [], "Failed to submit search request"
            
            logger.info(f"Search submitted with ID: {search_id}")
            
            # Wait for completion
            final_status = self._wait_for_completion(search_id)
            
            if final_status == SearchStatus.COMPLETED:
                # Get results
                routes = self._get_results(search_id)
                logger.info(f"Search completed successfully: {len(routes)} routes found")
                return routes, ""
            
            elif final_status == SearchStatus.FAILED:
                error_msg = self._get_error_message(search_id)
                logger.error(f"Search failed: {error_msg}")
                return [], error_msg
            
            elif final_status == SearchStatus.CANCELLED:
                logger.info("Search was cancelled")
                return [], "Search was cancelled"
            
            else:
                logger.warning(f"Search ended with unexpected status: {final_status}")
                return [], f"Search ended with status: {final_status}"
                
        except Exception as e:
            logger.error(f"Error in distributed search: {e}")
            return [], f"Distributed search error: {str(e)}"
    
    def _submit_search(self, search_request: Dict[str, Any]) -> Optional[str]:
        """Submit search request to the service."""
        try:
            response = self.session.post(
                f"{self.base_url}/search",
                json=search_request
            )
            response.raise_for_status()
            
            data = response.json()
            return data.get('search_id')
            
        except Exception as e:
            logger.error(f"Failed to submit search: {e}")
            return None
    
    def _wait_for_completion(self, search_id: str) -> SearchStatus:
        """Wait for search to complete."""
        start_time = time.time()
        
        while time.time() - start_time < self.config.max_wait_time:
            try:
                status_data = self._get_status(search_id)
                if not status_data:
                    logger.error("Failed to get search status")
                    return SearchStatus.FAILED
                
                current_status = SearchStatus(status_data.get('status'))
                
                if current_status in [SearchStatus.COMPLETED, SearchStatus.FAILED, SearchStatus.CANCELLED]:
                    return current_status
                
                # Log progress
                progress = status_data.get('progress', {})
                logger.info(f"Search progress: {progress.get('routes_found', 0)} routes found, "
                           f"{progress.get('active_tasks', 0)} active tasks")
                
                time.sleep(self.config.poll_interval)
                
            except Exception as e:
                logger.error(f"Error checking search status: {e}")
                time.sleep(self.config.poll_interval)
        
        logger.warning("Search timed out")
        return SearchStatus.FAILED
    
    def _get_status(self, search_id: str) -> Optional[Dict[str, Any]]:
        """Get search status."""
        try:
            response = self.session.get(f"{self.base_url}/search/{search_id}/status")
            response.raise_for_status()
            return response.json()
            
        except Exception as e:
            logger.error(f"Failed to get search status: {e}")
            return None
    
    def _get_results(self, search_id: str) -> List[Dict[str, Any]]:
        """Get search results."""
        try:
            response = self.session.get(f"{self.base_url}/search/{search_id}/results")
            response.raise_for_status()
            
            data = response.json()
            return data.get('routes', [])
            
        except Exception as e:
            logger.error(f"Failed to get search results: {e}")
            return []
    
    def _get_error_message(self, search_id: str) -> str:
        """Get error message for failed search."""
        try:
            status_data = self._get_status(search_id)
            if status_data:
                return status_data.get('error_message', 'Unknown error')
            return 'Failed to get error details'
            
        except Exception as e:
            logger.error(f"Failed to get error message: {e}")
            return 'Failed to get error details'
    
    def cancel_search(self, search_id: str) -> bool:
        """Cancel a running search."""
        try:
            response = self.session.delete(f"{self.base_url}/search/{search_id}")
            response.raise_for_status()
            return True
            
        except Exception as e:
            logger.error(f"Failed to cancel search: {e}")
            return False
    
    def health_check(self) -> bool:
        """Check if the service is healthy."""
        try:
            response = self.session.get(f"{self.base_url}/health")
            response.raise_for_status()
            
            data = response.json()
            return data.get('status') == 'healthy'
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False
    
    def get_queue_stats(self) -> Dict[str, Any]:
        """Get queue statistics."""
        try:
            response = self.session.get(f"{self.base_url}/queues/stats")
            response.raise_for_status()
            return response.json()
            
        except Exception as e:
            logger.error(f"Failed to get queue stats: {e}")
            return {}

# Replacement class for the original RetroSynthesisTreeSearch
class DistributedRetroSynthesisTreeSearch:
    """
    Drop-in replacement for RetroSynthesisTreeSearch that uses the distributed service.
    """
    
    def __init__(self, single_step_api=None, terminal_checker=None,
                 disconnection_validator=None, route_scorer=None,
                 synthesis_score_api=None, config=None):
        # These parameters are ignored since processing happens in the service
        self.config = config

        # Configure client with service URL from config
        client_config = DistributedSearchConfig()
        if config and hasattr(config, 'DISTRIBUTED_SEARCH_URL'):
            client_config.service_url = config.DISTRIBUTED_SEARCH_URL

        self.client = DistributedTreeSearchClient(client_config)
    
    def search(self, target_smiles: str, max_routes: int = None, kwargs: dict = None) -> Tuple[List[Dict], str]:
        """
        Perform tree search using the distributed service.
        
        Args:
            target_smiles: Target molecule SMILES
            max_routes: Maximum number of routes to return
            kwargs: Additional search parameters
            
        Returns:
            Tuple of (routes, error_message)
        """
        # Check if service is available
        if not self.client.health_check():
            logger.warning("Distributed service is not available - falling back to local processing")
            return [], "Distributed service is not available"
        
        return self.client.search(target_smiles, max_routes, kwargs)

# Factory function to create the appropriate search instance
def create_tree_search(use_distributed: bool = True, **kwargs):
    """
    Factory function to create tree search instance.
    
    Args:
        use_distributed: Whether to use distributed service
        **kwargs: Arguments for tree search initialization
        
    Returns:
        Tree search instance
    """
    if use_distributed:
        return DistributedRetroSynthesisTreeSearch(**kwargs)
    else:
        # Import and return the original implementation
        from core.tree.tree_search import RetroSynthesisTreeSearch
        return RetroSynthesisTreeSearch(**kwargs)
