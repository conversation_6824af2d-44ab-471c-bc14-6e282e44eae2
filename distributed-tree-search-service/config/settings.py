import os
from typing import Dict, Any

class Config:
    """Configuration settings for the distributed tree search service."""
    
    # Service Configuration
    SERVICE_NAME = "distributed-tree-search"
    SERVICE_VERSION = "1.0.0"
    API_HOST = os.getenv('API_HOST', '0.0.0.0')
    API_PORT = int(os.getenv('API_PORT', '8080'))
    DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'
    
    # Redis Configuration
    REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    REDIS_SENTINEL_URL = os.getenv('REDIS_SENTINEL_URL')
    REDIS_SENTINEL_SERVICE_NAME = os.getenv('REDIS_SENTINEL_SERVICE_NAME', 'mymaster')
    
    # Celery Configuration
    CELERY_BROKER_URL = os.getenv('CELERY_BROKER_URL', REDIS_URL)
    CELERY_RESULT_BACKEND = os.getenv('CELERY_RESULT_BACKEND', REDIS_URL)
    
    # Queue Configuration
    MOLECULE_EXPANSION_QUEUE = os.getenv('MOLECULE_EXPANSION_QUEUE', 'molecule_expansion_queue')
    REACTION_PROCESSING_QUEUE = os.getenv('REACTION_PROCESSING_QUEUE', 'reaction_processing_queue')
    RESULTS_AGGREGATION_QUEUE = os.getenv('RESULTS_AGGREGATION_QUEUE', 'results_aggregation_queue')
    COORDINATION_QUEUE = os.getenv('COORDINATION_QUEUE', 'coordination_queue')
    
    # Worker Configuration
    MAX_MOLECULE_WORKERS = int(os.getenv('MAX_MOLECULE_WORKERS', '10'))
    MAX_REACTION_WORKERS = int(os.getenv('MAX_REACTION_WORKERS', '20'))
    MOLECULE_WORKER_CONCURRENCY = int(os.getenv('MOLECULE_WORKER_CONCURRENCY', '4'))
    REACTION_WORKER_CONCURRENCY = int(os.getenv('REACTION_WORKER_CONCURRENCY', '8'))
    
    # Processing Configuration
    QUEUE_BATCH_SIZE = int(os.getenv('QUEUE_BATCH_SIZE', '50'))
    MAX_QUEUE_SIZE = int(os.getenv('MAX_QUEUE_SIZE', '10000'))
    QUEUE_TIMEOUT = int(os.getenv('QUEUE_TIMEOUT', '300'))  # 5 minutes
    
    # Task Timeouts
    MOLECULE_TASK_TIMEOUT = int(os.getenv('MOLECULE_TASK_TIMEOUT', '600'))  # 10 minutes
    REACTION_TASK_TIMEOUT = int(os.getenv('REACTION_TASK_TIMEOUT', '300'))  # 5 minutes
    COORDINATION_TASK_TIMEOUT = int(os.getenv('COORDINATION_TASK_TIMEOUT', '3600'))  # 1 hour
    
    # Tree Search Parameters
    MAX_DEPTH = int(os.getenv('MAX_DEPTH', '3'))
    BEAM_WIDTH = int(os.getenv('BEAM_WIDTH', '50'))
    PRUNING_FACTOR = float(os.getenv('PRUNING_FACTOR', '0.1'))
    MAX_ROUTES = int(os.getenv('MAX_ROUTES', '100'))
    BEAM_BASED_PRUNING = int(os.getenv('BEAM_BASED_PRUNING', '1'))
    
    # External API Configuration
    AZURE_HOSTNAME = os.getenv('AZURE_HOSTNAME', "***********")
    ASKOS_PREDICTIONS_PISTACHO_URL = f"http://{AZURE_HOSTNAME}:9420/predictions/pistachio_23Q3"
    ASKCOS_EM_URL = f"http://{AZURE_HOSTNAME}:9451/predictions"
    REACTION_CLASS_URL = f"http://{AZURE_HOSTNAME}:9621/reaction_class"
    ASKOS_PRE_REAXYS = f"http://{AZURE_HOSTNAME}:9410/predictions/reaxys"
    
    # Validation Configuration
    SYNTHESIS_SCORE_THRESHOLD = float(os.getenv('SYNTHESIS_SCORE_THRESHOLD', '1.05'))
    MIN_FORWARD_PROB = float(os.getenv('MIN_FORWARD_PROB', '0.7'))
    MIN_CERTAINITY_SCORE = float(os.getenv('MIN_CERTAINITY_SCORE', '0.7'))
    HEAVY_METAL_THRESHOLD = float(os.getenv('HEAVY_METAL_THRESHOLD', '31.0'))
    
    # Database Configuration
    MONGODB_URL = os.getenv('MONGODB_URL', 'mongodb://localhost:27017/')
    DATABASE_NAME = os.getenv('DATABASE_NAME', 'retro_synthesis')
    
    # Storage Configuration
    AZURE_BLOB_CONNECTION_STRING = os.getenv('AZURE_BLOB_CONNECTION_STRING', '')
    AZURE_BLOB_CONTAINER_NAME = os.getenv('AZURE_BLOB_CONTAINER_NAME', 'imagestorage')
    
    # Monitoring Configuration
    ENABLE_METRICS = os.getenv('ENABLE_METRICS', 'True').lower() == 'true'
    METRICS_PORT = int(os.getenv('METRICS_PORT', '9090'))
    
    # Logging Configuration
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    @classmethod
    def get_redis_config(cls):
        """Get Redis configuration with sentinel support."""
        if cls.REDIS_SENTINEL_URL:
            sentinel_str = cls.REDIS_SENTINEL_URL.replace('/0', '').split("sentinel://")
            sentinel_hosts = [(sentinel_str[1].split(":")[0], int(sentinel_str[1].split(":")[1]))]
            
            return {
                'sentinel_hosts': sentinel_hosts,
                'service_name': cls.REDIS_SENTINEL_SERVICE_NAME,
                'transport_options': {
                    'master_name': cls.REDIS_SENTINEL_SERVICE_NAME,
                    'sentinels': sentinel_hosts,
                    'sentinel_kwargs': {
                        'socket_timeout': 5,
                        'socket_connect_timeout': 5,
                    },
                    'socket_keepalive': True,
                    'retry_on_timeout': True,
                    'health_check_interval': 30,
                }
            }
        else:
            return {'url': cls.REDIS_URL}
