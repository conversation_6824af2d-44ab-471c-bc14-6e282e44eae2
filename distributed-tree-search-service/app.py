#!/usr/bin/env python3
"""
Distributed Tree Search Service
Main Flask application entry point.
"""

import os
import logging
from flask import Flask
from flask_cors import CORS
from datetime import datetime

from config.settings import Config
from api.routes import api_bp
from utils.logging_config import setup_logging

def create_app():
    """Create and configure Flask application."""
    app = Flask(__name__)
    
    # Configure CORS
    CORS(app, resources={
        r"/api/*": {
            "origins": "*",
            "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization"]
        }
    })
    
    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)
    
    # Register blueprints
    app.register_blueprint(api_bp, url_prefix='/api/v1')
    
    # Health check endpoint at root
    @app.route('/')
    def root():
        return {
            "service": Config.SERVICE_NAME,
            "version": Config.SERVICE_VERSION,
            "status": "running",
            "timestamp": datetime.utcnow().isoformat()
        }
    
    # Error handlers
    @app.errorhandler(404)
    def not_found(error):
        return {
            "error": "not_found",
            "message": "Endpoint not found",
            "timestamp": datetime.utcnow().isoformat()
        }, 404
    
    @app.errorhandler(500)
    def internal_error(error):
        logger.error(f"Internal server error: {error}")
        return {
            "error": "internal_server_error",
            "message": "An internal error occurred",
            "timestamp": datetime.utcnow().isoformat()
        }, 500
    
    logger.info(f"Flask app created - {Config.SERVICE_NAME} v{Config.SERVICE_VERSION}")
    return app

def main():
    """Main entry point."""
    app = create_app()
    
    logger = logging.getLogger(__name__)
    logger.info(f"Starting {Config.SERVICE_NAME} on {Config.API_HOST}:{Config.API_PORT}")
    
    # Run the application
    app.run(
        host=Config.API_HOST,
        port=Config.API_PORT,
        debug=Config.DEBUG,
        threaded=True
    )

if __name__ == '__main__':
    main()
