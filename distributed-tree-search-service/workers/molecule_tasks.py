"""
Molecule expansion tasks for distributed tree search.
These tasks handle the expansion of molecule nodes by finding possible reactions.
"""

import logging
import traceback
from typing import Dict, List, Any, Optional
from celery import Task
from celery.utils.log import get_task_logger

from workers.celery_app import celery_app
from core.tree_nodes import MoleculeN<PERSON>, ReactionNode, NodeStatus
from core.single_step_api import SingleStepAP<PERSON>
from core.terminal_checker import <PERSON><PERSON><PERSON><PERSON>
from core.disconnection_validator import DisconnectionValidator
from utils.redis_client import RedisClient
from utils.helpers import canonicalize_smiles
from config.settings import Config

logger = get_task_logger(__name__)

class MoleculeExpansionTask(Task):
    """Base class for molecule expansion tasks with shared resources."""
    
    def __init__(self):
        self.single_step_api = None
        self.terminal_checker = None
        self.disconnection_validator = None
        self.redis_client = None
    
    def __call__(self, *args, **kwargs):
        if not self.single_step_api:
            self.single_step_api = SingleStepAPI()
            self.terminal_checker = TerminalChecker()
            self.disconnection_validator = DisconnectionValidator()
            self.redis_client = RedisClient()
        return super().__call__(*args, **kwargs)

@celery_app.task(
    bind=True,
    base=MoleculeExpansionTask,
    name='expand_molecule',
    max_retries=3,
    default_retry_delay=60
)
def expand_molecule_task(self, search_id: str, molecule_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Expand a molecule node by finding possible reactions.
    
    Args:
        search_id: Unique identifier for the search session
        molecule_data: Dictionary containing molecule node information
        
    Returns:
        Dictionary containing expansion results
    """
    try:
        logger.info(f"[{search_id}] Expanding molecule: {molecule_data.get('smiles')}")
        
        # Create molecule node from data
        mol_node = MoleculeNode(
            smiles=molecule_data['smiles'],
            depth=molecule_data.get('depth', 0),
            node_id=molecule_data.get('node_id')
        )
        
        # Check if already terminal
        if self.terminal_checker.is_terminal(mol_node):
            logger.info(f"[{search_id}] Molecule {mol_node.smiles} is terminal")
            return {
                'search_id': search_id,
                'molecule_id': mol_node.node_id,
                'status': 'terminal',
                'reactions': [],
                'message': 'Molecule is a building block'
            }
        
        # Get disconnections from single-step API
        disconnections_df = self.single_step_api.get_retrosynthesis_reactions(mol_node.smiles)
        
        if disconnections_df.empty:
            logger.warning(f"[{search_id}] No disconnections found for {mol_node.smiles}")
            return {
                'search_id': search_id,
                'molecule_id': mol_node.node_id,
                'status': 'no_disconnections',
                'reactions': [],
                'message': 'No valid disconnections found'
            }
        
        # Process disconnections
        valid_reactions = []
        total_disconnections = len(disconnections_df)
        
        logger.info(f"[{search_id}] Processing {total_disconnections} disconnections for {mol_node.smiles}")
        
        for idx, (_, row) in enumerate(disconnections_df.iterrows()):
            try:
                if self.disconnection_validator.is_valid(row, mol_node.smiles):
                    reaction_data = {
                        'reaction_id': f"{mol_node.node_id}_rxn_{idx}",
                        'parent_molecule_id': mol_node.node_id,
                        'reaction_data': row.to_dict(),
                        'reaction_score': row.get('ltr_score_scaled', 0.0),
                        'depth': mol_node.depth + 1,
                        'search_id': search_id
                    }
                    valid_reactions.append(reaction_data)
                    
            except Exception as e:
                logger.error(f"[{search_id}] Error processing disconnection {idx}: {e}")
                continue
        
        logger.info(f"[{search_id}] Found {len(valid_reactions)} valid reactions for {mol_node.smiles}")
        
        # Apply pruning if needed
        if len(valid_reactions) > Config.BEAM_WIDTH:
            # Sort by reaction score and take top reactions
            valid_reactions.sort(key=lambda x: x['reaction_score'], reverse=True)
            valid_reactions = valid_reactions[:Config.BEAM_WIDTH]
            logger.info(f"[{search_id}] Pruned to top {len(valid_reactions)} reactions")
        
        # Queue reaction processing tasks
        reaction_task_ids = []
        for reaction_data in valid_reactions:
            task_result = celery_app.send_task(
                'process_reaction',
                args=[search_id, reaction_data],
                queue=Config.REACTION_PROCESSING_QUEUE
            )
            reaction_task_ids.append(task_result.id)
        
        return {
            'search_id': search_id,
            'molecule_id': mol_node.node_id,
            'status': 'expanded',
            'reactions': valid_reactions,
            'reaction_task_ids': reaction_task_ids,
            'total_disconnections': total_disconnections,
            'valid_reactions': len(valid_reactions),
            'message': f'Successfully expanded molecule with {len(valid_reactions)} reactions'
        }
        
    except Exception as e:
        logger.error(f"[{search_id}] Error expanding molecule {molecule_data.get('smiles')}: {e}")
        logger.error(f"[{search_id}] Traceback: {traceback.format_exc()}")
        
        # Retry logic
        if self.request.retries < self.max_retries:
            logger.info(f"[{search_id}] Retrying molecule expansion (attempt {self.request.retries + 1})")
            raise self.retry(countdown=60 * (2 ** self.request.retries))
        
        return {
            'search_id': search_id,
            'molecule_id': molecule_data.get('node_id'),
            'status': 'failed',
            'reactions': [],
            'error': str(e),
            'message': f'Failed to expand molecule: {str(e)}'
        }

@celery_app.task(
    bind=True,
    base=MoleculeExpansionTask,
    name='batch_expand_molecules',
    max_retries=2
)
def batch_expand_molecules_task(self, search_id: str, molecules_batch: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Expand multiple molecules in a batch for efficiency.
    
    Args:
        search_id: Unique identifier for the search session
        molecules_batch: List of molecule data dictionaries
        
    Returns:
        Dictionary containing batch expansion results
    """
    try:
        logger.info(f"[{search_id}] Batch expanding {len(molecules_batch)} molecules")
        
        results = []
        for molecule_data in molecules_batch:
            try:
                result = expand_molecule_task.apply(args=[search_id, molecule_data]).get()
                results.append(result)
            except Exception as e:
                logger.error(f"[{search_id}] Error in batch expansion for molecule {molecule_data.get('smiles')}: {e}")
                results.append({
                    'search_id': search_id,
                    'molecule_id': molecule_data.get('node_id'),
                    'status': 'failed',
                    'error': str(e)
                })
        
        successful = len([r for r in results if r.get('status') not in ['failed', 'error']])
        
        return {
            'search_id': search_id,
            'batch_size': len(molecules_batch),
            'successful': successful,
            'failed': len(molecules_batch) - successful,
            'results': results,
            'message': f'Batch expansion completed: {successful}/{len(molecules_batch)} successful'
        }
        
    except Exception as e:
        logger.error(f"[{search_id}] Error in batch molecule expansion: {e}")
        return {
            'search_id': search_id,
            'status': 'failed',
            'error': str(e),
            'message': f'Batch expansion failed: {str(e)}'
        }
