"""
Coordination tasks for distributed tree search.
These tasks orchestrate the overall search process and manage the distributed workflow.
"""

import logging
import time
import traceback
from typing import Dict, List, Any, Optional
from celery import Task, group, chord
from celery.utils.log import get_task_logger
from datetime import datetime, timedelta

from workers.celery_app import celery_app
from core.tree_nodes import MoleculeNode, NodeStatus
from core.terminal_checker import <PERSON><PERSON><PERSON><PERSON>
from utils.redis_client import RedisClient
from utils.helpers import canonicalize_smiles
from config.settings import Config
from api.models import SearchStatus

logger = get_task_logger(__name__)

class CoordinationTask(Task):
    """Base class for coordination tasks with shared resources."""
    
    def __init__(self):
        self.terminal_checker = None
        self.redis_client = None
    
    def __call__(self, *args, **kwargs):
        if not self.terminal_checker:
            self.terminal_checker = TerminalChecker()
            self.redis_client = RedisClient()
        return super().__call__(*args, **kwargs)

@celery_app.task(
    bind=True,
    base=CoordinationTask,
    name='coordinate_search',
    max_retries=1
)
def coordinate_search_task(self, search_id: str, search_request: Dict[str, Any]) -> Dict[str, Any]:
    """
    Main coordination task that orchestrates the distributed tree search.
    
    Args:
        search_id: Unique identifier for the search session
        search_request: Dictionary containing search parameters
        
    Returns:
        Dictionary containing search coordination results
    """
    try:
        logger.info(f"[{search_id}] Starting distributed tree search coordination")
        
        # Initialize search state in Redis
        search_state = {
            'search_id': search_id,
            'status': SearchStatus.RUNNING,
            'target_smiles': search_request['target_smiles'],
            'max_routes': search_request.get('max_routes', Config.MAX_ROUTES),
            'max_depth': search_request.get('max_depth', Config.MAX_DEPTH),
            'beam_width': search_request.get('beam_width', Config.BEAM_WIDTH),
            'created_at': datetime.utcnow().isoformat(),
            'updated_at': datetime.utcnow().isoformat(),
            'progress': {
                'current_depth': 0,
                'molecules_processed': 0,
                'reactions_processed': 0,
                'routes_found': 0,
                'active_tasks': 0
            }
        }
        
        self.redis_client.store_search_state(search_id, search_state)
        
        # Canonicalize target SMILES
        target_smiles = canonicalize_smiles(search_request['target_smiles'])
        
        # Check if target is already terminal
        root_molecule = MoleculeNode(smiles=target_smiles, depth=0)
        if self.terminal_checker.is_terminal(root_molecule):
            logger.info(f"[{search_id}] Target molecule is terminal - no synthesis needed")
            
            search_state.update({
                'status': SearchStatus.COMPLETED,
                'completed_at': datetime.utcnow().isoformat(),
                'message': 'Target molecule is a building block - no synthesis needed'
            })
            self.redis_client.store_search_state(search_id, search_state)
            
            return {
                'search_id': search_id,
                'status': 'completed',
                'message': 'Target is terminal',
                'routes_found': 0
            }
        
        # Start the search with the root molecule
        root_molecule_data = {
            'node_id': f"{search_id}_root",
            'smiles': target_smiles,
            'depth': 0,
            'ancestry_smiles': [],
            'search_id': search_id
        }
        
        # Submit initial molecule expansion
        expansion_task = celery_app.send_task(
            'expand_molecule',
            args=[search_id, root_molecule_data],
            queue=Config.MOLECULE_EXPANSION_QUEUE
        )
        
        # Start monitoring task
        monitor_task = celery_app.send_task(
            'monitor_search_progress',
            args=[search_id],
            queue=Config.COORDINATION_QUEUE,
            countdown=30  # Start monitoring after 30 seconds
        )
        
        # Update search state
        search_state['progress']['active_tasks'] = 1
        search_state['initial_task_id'] = expansion_task.id
        search_state['monitor_task_id'] = monitor_task.id
        self.redis_client.store_search_state(search_id, search_state)
        
        logger.info(f"[{search_id}] Search coordination initiated with task {expansion_task.id}")
        
        return {
            'search_id': search_id,
            'status': 'running',
            'initial_task_id': expansion_task.id,
            'monitor_task_id': monitor_task.id,
            'message': 'Search coordination started successfully'
        }
        
    except Exception as e:
        logger.error(f"[{search_id}] Error in search coordination: {e}")
        logger.error(f"[{search_id}] Traceback: {traceback.format_exc()}")
        
        # Update search state to failed
        try:
            search_state = self.redis_client.get_search_state(search_id) or {}
            search_state.update({
                'status': SearchStatus.FAILED,
                'error_message': str(e),
                'updated_at': datetime.utcnow().isoformat()
            })
            self.redis_client.store_search_state(search_id, search_state)
        except:
            pass
        
        return {
            'search_id': search_id,
            'status': 'failed',
            'error': str(e),
            'message': f'Search coordination failed: {str(e)}'
        }

@celery_app.task(
    bind=True,
    base=CoordinationTask,
    name='monitor_search_progress'
)
def monitor_search_progress_task(self, search_id: str) -> Dict[str, Any]:
    """
    Monitor the progress of a distributed search and handle completion.
    
    Args:
        search_id: Unique identifier for the search session
        
    Returns:
        Dictionary containing monitoring results
    """
    try:
        logger.info(f"[{search_id}] Monitoring search progress")
        
        search_state = self.redis_client.get_search_state(search_id)
        if not search_state:
            logger.error(f"[{search_id}] Search state not found")
            return {'search_id': search_id, 'status': 'error', 'message': 'Search state not found'}
        
        # Check if search is still running
        if search_state.get('status') != SearchStatus.RUNNING:
            logger.info(f"[{search_id}] Search is no longer running: {search_state.get('status')}")
            return {'search_id': search_id, 'status': search_state.get('status')}
        
        # Get active task count and progress metrics
        active_tasks = self.redis_client.get_active_task_count(search_id)
        progress_metrics = self.redis_client.get_progress_metrics(search_id)
        
        # Check for completion conditions
        routes_found = progress_metrics.get('routes_found', 0)
        max_routes = search_state.get('max_routes', Config.MAX_ROUTES)
        
        # Update progress
        search_state['progress'].update({
            'active_tasks': active_tasks,
            'molecules_processed': progress_metrics.get('molecules_processed', 0),
            'reactions_processed': progress_metrics.get('reactions_processed', 0),
            'routes_found': routes_found,
            'current_depth': progress_metrics.get('current_depth', 0)
        })
        search_state['updated_at'] = datetime.utcnow().isoformat()
        
        # Check completion conditions
        if routes_found >= max_routes:
            logger.info(f"[{search_id}] Search completed - found {routes_found} routes (target: {max_routes})")
            
            # Trigger results aggregation
            aggregation_task = celery_app.send_task(
                'aggregate_search_results',
                args=[search_id],
                queue=Config.RESULTS_AGGREGATION_QUEUE
            )
            
            search_state.update({
                'status': SearchStatus.COMPLETED,
                'completed_at': datetime.utcnow().isoformat(),
                'aggregation_task_id': aggregation_task.id,
                'message': f'Search completed with {routes_found} routes found'
            })
            
        elif active_tasks == 0:
            # No more active tasks - search is complete
            logger.info(f"[{search_id}] Search completed - no more active tasks")
            
            # Trigger results aggregation
            aggregation_task = celery_app.send_task(
                'aggregate_search_results',
                args=[search_id],
                queue=Config.RESULTS_AGGREGATION_QUEUE
            )
            
            search_state.update({
                'status': SearchStatus.COMPLETED,
                'completed_at': datetime.utcnow().isoformat(),
                'aggregation_task_id': aggregation_task.id,
                'message': f'Search completed with {routes_found} routes found'
            })
            
        else:
            # Continue monitoring
            logger.info(f"[{search_id}] Search in progress - {active_tasks} active tasks, {routes_found} routes found")
            
            # Schedule next monitoring check
            celery_app.send_task(
                'monitor_search_progress',
                args=[search_id],
                queue=Config.COORDINATION_QUEUE,
                countdown=60  # Check again in 60 seconds
            )
        
        # Store updated state
        self.redis_client.store_search_state(search_id, search_state)
        
        return {
            'search_id': search_id,
            'status': search_state.get('status'),
            'active_tasks': active_tasks,
            'routes_found': routes_found,
            'message': f'Monitoring update: {active_tasks} active tasks, {routes_found} routes'
        }
        
    except Exception as e:
        logger.error(f"[{search_id}] Error monitoring search progress: {e}")
        logger.error(f"[{search_id}] Traceback: {traceback.format_exc()}")
        
        return {
            'search_id': search_id,
            'status': 'error',
            'error': str(e),
            'message': f'Monitoring failed: {str(e)}'
        }

@celery_app.task(
    bind=True,
    base=CoordinationTask,
    name='cancel_search'
)
def cancel_search_task(self, search_id: str) -> Dict[str, Any]:
    """
    Cancel a running search and clean up resources.
    
    Args:
        search_id: Unique identifier for the search session
        
    Returns:
        Dictionary containing cancellation results
    """
    try:
        logger.info(f"[{search_id}] Cancelling search")
        
        # Update search state
        search_state = self.redis_client.get_search_state(search_id) or {}
        search_state.update({
            'status': SearchStatus.CANCELLED,
            'cancelled_at': datetime.utcnow().isoformat(),
            'updated_at': datetime.utcnow().isoformat(),
            'message': 'Search cancelled by user'
        })
        
        self.redis_client.store_search_state(search_id, search_state)
        
        # Revoke active tasks (best effort)
        try:
            active_task_ids = self.redis_client.get_active_task_ids(search_id)
            if active_task_ids:
                celery_app.control.revoke(active_task_ids, terminate=True)
                logger.info(f"[{search_id}] Revoked {len(active_task_ids)} active tasks")
        except Exception as e:
            logger.warning(f"[{search_id}] Could not revoke all tasks: {e}")
        
        # Clean up Redis data
        self.redis_client.cleanup_search_data(search_id)
        
        return {
            'search_id': search_id,
            'status': 'cancelled',
            'message': 'Search cancelled successfully'
        }
        
    except Exception as e:
        logger.error(f"[{search_id}] Error cancelling search: {e}")
        return {
            'search_id': search_id,
            'status': 'error',
            'error': str(e),
            'message': f'Cancellation failed: {str(e)}'
        }
