"""
Results aggregation tasks for distributed tree search.
These tasks collect and process the final search results.
"""

import logging
import traceback
from typing import Dict, List, Any, Optional
from celery import Task
from celery.utils.log import get_task_logger
from datetime import datetime

from workers.celery_app import celery_app
from utils.redis_client import RedisClient
from api.models import SearchStatus
from config.settings import Config

logger = get_task_logger(__name__)

class AggregationTask(Task):
    """Base class for aggregation tasks with shared resources."""
    
    def __init__(self):
        self.redis_client = None
    
    def __call__(self, *args, **kwargs):
        if not self.redis_client:
            self.redis_client = RedisClient()
        return super().__call__(*args, **kwargs)

@celery_app.task(
    bind=True,
    base=AggregationTask,
    name='aggregate_search_results',
    max_retries=2
)
def aggregate_search_results_task(self, search_id: str) -> Dict[str, Any]:
    """
    Aggregate final search results from distributed processing.
    
    Args:
        search_id: Unique identifier for the search session
        
    Returns:
        Dictionary containing aggregation results
    """
    try:
        logger.info(f"[{search_id}] Aggregating search results")
        
        # Get search state
        search_state = self.redis_client.get_search_state(search_id)
        if not search_state:
            raise ValueError(f"Search state not found for {search_id}")
        
        # Get all reaction results
        reaction_results = self.redis_client.get_reaction_results(search_id)
        
        # Extract complete routes
        routes = self._extract_complete_routes(search_id, reaction_results)
        
        # Score and sort routes
        scored_routes = self._score_and_sort_routes(routes)
        
        # Limit to max routes
        max_routes = search_state.get('max_routes', Config.MAX_ROUTES)
        final_routes = scored_routes[:max_routes]
        
        # Prepare final results
        results = {
            'search_id': search_id,
            'status': SearchStatus.COMPLETED,
            'target_smiles': search_state.get('target_smiles'),
            'num_routes': len(final_routes),
            'routes': final_routes,
            'created_at': search_state.get('created_at'),
            'completed_at': datetime.utcnow().isoformat(),
            'processing_time': self._calculate_processing_time(search_state),
            'statistics': {
                'total_reactions_processed': len(reaction_results),
                'complete_routes_found': len(routes),
                'final_routes_returned': len(final_routes)
            }
        }
        
        # Store results
        self.redis_client.store_search_results(search_id, results)
        
        # Update search state
        search_state.update({
            'status': SearchStatus.COMPLETED,
            'completed_at': datetime.utcnow().isoformat(),
            'num_routes_found': len(final_routes)
        })
        self.redis_client.store_search_state(search_id, search_state)
        
        logger.info(f"[{search_id}] Results aggregated: {len(final_routes)} routes found")
        
        return {
            'search_id': search_id,
            'status': 'completed',
            'num_routes': len(final_routes),
            'message': f'Results aggregated successfully: {len(final_routes)} routes found'
        }
        
    except Exception as e:
        logger.error(f"[{search_id}] Error aggregating results: {e}")
        logger.error(f"[{search_id}] Traceback: {traceback.format_exc()}")
        
        # Update search state to failed
        try:
            search_state = self.redis_client.get_search_state(search_id) or {}
            search_state.update({
                'status': SearchStatus.FAILED,
                'error_message': str(e),
                'failed_at': datetime.utcnow().isoformat()
            })
            self.redis_client.store_search_state(search_id, search_state)
        except:
            pass
        
        return {
            'search_id': search_id,
            'status': 'failed',
            'error': str(e),
            'message': f'Results aggregation failed: {str(e)}'
        }
    
    def _extract_complete_routes(self, search_id: str, reaction_results: Dict[str, Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Extract complete synthesis routes from reaction results.
        
        Args:
            search_id: Search identifier
            reaction_results: Dictionary of reaction results
            
        Returns:
            List of complete route dictionaries
        """
        try:
            # This is a simplified route extraction
            # In a full implementation, you would reconstruct the tree structure
            # and extract all complete paths from root to terminal nodes
            
            complete_routes = []
            
            # Group reactions by depth and build routes
            solved_reactions = [
                result for result in reaction_results.values()
                if result.get('is_solved', False)
            ]
            
            # For now, treat each solved reaction as a potential route
            # This is simplified - you'd need proper tree reconstruction
            for reaction_result in solved_reactions:
                route = {
                    'route_id': len(complete_routes) + 1,
                    'reactions': [reaction_result],
                    'num_steps': 1,
                    'total_score': reaction_result.get('reaction_score', 0.0),
                    'is_complete': True
                }
                complete_routes.append(route)
            
            logger.info(f"[{search_id}] Extracted {len(complete_routes)} complete routes")
            return complete_routes
            
        except Exception as e:
            logger.error(f"[{search_id}] Error extracting routes: {e}")
            return []
    
    def _score_and_sort_routes(self, routes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Score and sort routes by quality.
        
        Args:
            routes: List of route dictionaries
            
        Returns:
            Sorted list of routes (best first)
        """
        try:
            # Simple scoring based on reaction scores
            for route in routes:
                total_score = 0.0
                num_reactions = len(route.get('reactions', []))
                
                for reaction in route.get('reactions', []):
                    reaction_score = reaction.get('reaction_score', 0.0)
                    total_score += reaction_score
                
                # Average score per reaction
                route['average_score'] = total_score / max(1, num_reactions)
                route['total_score'] = total_score
            
            # Sort by average score (higher is better)
            routes.sort(key=lambda x: x.get('average_score', 0.0), reverse=True)
            
            return routes
            
        except Exception as e:
            logger.error(f"Error scoring routes: {e}")
            return routes
    
    def _calculate_processing_time(self, search_state: Dict[str, Any]) -> Optional[float]:
        """
        Calculate total processing time.
        
        Args:
            search_state: Search state dictionary
            
        Returns:
            Processing time in seconds or None
        """
        try:
            created_at = search_state.get('created_at')
            if created_at:
                start_time = datetime.fromisoformat(created_at)
                end_time = datetime.utcnow()
                return (end_time - start_time).total_seconds()
            return None
            
        except Exception as e:
            logger.error(f"Error calculating processing time: {e}")
            return None
