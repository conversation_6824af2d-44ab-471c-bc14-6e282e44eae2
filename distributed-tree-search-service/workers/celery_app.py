from celery import Celery
from celery.signals import after_setup_logger
import logging
import os
from config.settings import Config

def create_celery_app():
    """Create and configure Celery application."""
    
    # Get Redis configuration
    redis_config = Config.get_redis_config()
    
    # Create Celery app
    celery_app = Celery(
        Config.SERVICE_NAME,
        broker=Config.CELERY_BROKER_URL,
        backend=Config.CELERY_RESULT_BACKEND,
        include=[
            'workers.molecule_tasks',
            'workers.reaction_tasks', 
            'workers.coordination_tasks',
            'workers.aggregation_tasks'
        ]
    )
    
    # Configure Celery
    celery_config = {
        'task_serializer': 'json',
        'accept_content': ['json'],
        'result_serializer': 'json',
        'timezone': 'UTC',
        'enable_utc': True,
        'worker_prefetch_multiplier': 1,
        'task_acks_late': True,
        'worker_disable_rate_limits': True,
        'task_reject_on_worker_lost': True,
        'task_ignore_result': False,
        'worker_max_tasks_per_child': 100,
        'task_soft_time_limit': 300,
        'task_time_limit': 600,
        'worker_send_task_events': True,
        'task_send_sent_event': True,
        
        # Queue routing
        'task_routes': {
            'workers.molecule_tasks.*': {'queue': Config.MOLECULE_EXPANSION_QUEUE},
            'workers.reaction_tasks.*': {'queue': Config.REACTION_PROCESSING_QUEUE},
            'workers.coordination_tasks.*': {'queue': Config.COORDINATION_QUEUE},
            'workers.aggregation_tasks.*': {'queue': Config.RESULTS_AGGREGATION_QUEUE},
        },
        
        # Task timeouts
        'task_annotations': {
            'workers.molecule_tasks.*': {'time_limit': Config.MOLECULE_TASK_TIMEOUT},
            'workers.reaction_tasks.*': {'time_limit': Config.REACTION_TASK_TIMEOUT},
            'workers.coordination_tasks.*': {'time_limit': Config.COORDINATION_TASK_TIMEOUT},
        }
    }
    
    # Add Redis transport options if using Sentinel
    if 'transport_options' in redis_config:
        celery_config.update({
            'broker_transport_options': redis_config['transport_options'],
            'result_backend_transport_options': redis_config['transport_options']
        })
    
    celery_app.conf.update(celery_config)
    
    return celery_app

# Create the Celery app instance
celery_app = create_celery_app()

@after_setup_logger.connect
def setup_celery_logger(logger, *args, **kwargs):
    """Setup Celery logger."""
    os.makedirs('logs', exist_ok=True)
    
    # File handler
    fh = logging.FileHandler('logs/celery.log')
    fh.setLevel(logging.INFO)
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    fh.setFormatter(formatter)
    logger.addHandler(fh)
    
    # Console handler
    ch = logging.StreamHandler()
    ch.setLevel(logging.INFO)
    ch.setFormatter(formatter)
    logger.addHandler(ch)
