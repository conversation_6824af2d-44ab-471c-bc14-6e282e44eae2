"""
Reaction processing tasks for distributed tree search.
These tasks handle the processing of reaction nodes and creation of precursor molecules.
"""

import logging
import traceback
from typing import Dict, List, Any, Optional
from celery import Task
from celery.utils.log import get_task_logger

from workers.celery_app import celery_app
from core.tree_nodes import MoleculeNode, ReactionNode, NodeStatus
from core.terminal_checker import <PERSON><PERSON><PERSON><PERSON>
from api.synthesis_score_api import SynthesisScoreAPI
from utils.redis_client import RedisClient
from utils.helpers import canonicalize_smiles
from config.settings import Config

logger = get_task_logger(__name__)

class ReactionProcessingTask(Task):
    """Base class for reaction processing tasks with shared resources."""
    
    def __init__(self):
        self.terminal_checker = None
        self.synthesis_score_api = None
        self.redis_client = None
    
    def __call__(self, *args, **kwargs):
        if not self.terminal_checker:
            self.terminal_checker = TerminalChecker()
            self.synthesis_score_api = SynthesisScoreAPI()
            self.redis_client = RedisClient()
        return super().__call__(*args, **kwargs)

@celery_app.task(
    bind=True,
    base=ReactionProcessingTask,
    name='process_reaction',
    max_retries=3,
    default_retry_delay=30
)
def process_reaction_task(self, search_id: str, reaction_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Process a reaction node by creating precursor molecule nodes.
    
    Args:
        search_id: Unique identifier for the search session
        reaction_data: Dictionary containing reaction information
        
    Returns:
        Dictionary containing processing results
    """
    try:
        logger.info(f"[{search_id}] Processing reaction: {reaction_data.get('reaction_id')}")
        
        # Parse reactants from the reaction data
        retro_smiles = reaction_data['reaction_data'].get('Retro', '')
        reactants_smiles = [smiles.strip() for smiles in retro_smiles.split('.') if smiles.strip()]
        
        if not reactants_smiles:
            logger.warning(f"[{search_id}] No reactants found in reaction {reaction_data.get('reaction_id')}")
            return {
                'search_id': search_id,
                'reaction_id': reaction_data.get('reaction_id'),
                'status': 'invalid',
                'precursors': [],
                'message': 'No reactants found'
            }
        
        # Check for round trips (if ancestry data is available)
        ancestry_smiles = reaction_data.get('ancestry_smiles', [])
        
        precursors = []
        terminal_count = 0
        
        for idx, reactant_smiles in enumerate(reactants_smiles):
            try:
                # Canonicalize SMILES
                canonical_smiles = canonicalize_smiles(reactant_smiles)
                
                # Check for round trip
                if canonical_smiles in ancestry_smiles:
                    logger.info(f"[{search_id}] Round trip detected for {canonical_smiles}")
                    return {
                        'search_id': search_id,
                        'reaction_id': reaction_data.get('reaction_id'),
                        'status': 'round_trip',
                        'precursors': [],
                        'round_trip_molecule': canonical_smiles,
                        'message': f'Round trip detected: {canonical_smiles}'
                    }
                
                # Create molecule node
                precursor_id = f"{reaction_data.get('reaction_id')}_precursor_{idx}"
                precursor_data = {
                    'node_id': precursor_id,
                    'smiles': canonical_smiles,
                    'depth': reaction_data.get('depth', 1),
                    'parent_reaction_id': reaction_data.get('reaction_id'),
                    'search_id': search_id
                }
                
                # Check if terminal
                mol_node = MoleculeNode(smiles=canonical_smiles)
                is_terminal = self.terminal_checker.is_terminal(mol_node)
                
                if is_terminal:
                    terminal_count += 1
                    precursor_data['status'] = 'terminal'
                    precursor_data['synthesis_score'] = None
                else:
                    precursor_data['status'] = 'expandable'
                    # Get synthesis score
                    try:
                        synthesis_score = self.synthesis_score_api.get_synthesis_score(canonical_smiles)
                        precursor_data['synthesis_score'] = synthesis_score
                    except Exception as e:
                        logger.warning(f"[{search_id}] Could not get synthesis score for {canonical_smiles}: {e}")
                        precursor_data['synthesis_score'] = None
                
                precursors.append(precursor_data)
                
            except Exception as e:
                logger.error(f"[{search_id}] Error processing reactant {reactant_smiles}: {e}")
                continue
        
        if not precursors:
            return {
                'search_id': search_id,
                'reaction_id': reaction_data.get('reaction_id'),
                'status': 'failed',
                'precursors': [],
                'message': 'Failed to process any reactants'
            }
        
        # Determine if reaction is solved (all precursors are terminal)
        is_solved = terminal_count == len(precursors)
        
        # Queue expandable precursors for further expansion
        expansion_task_ids = []
        if not is_solved:
            expandable_precursors = [p for p in precursors if p['status'] == 'expandable']
            
            # Check depth limit
            current_depth = reaction_data.get('depth', 1)
            if current_depth < Config.MAX_DEPTH:
                for precursor in expandable_precursors:
                    # Add current ancestry to prevent round trips
                    precursor['ancestry_smiles'] = ancestry_smiles + [precursor['smiles']]
                    
                    task_result = celery_app.send_task(
                        'expand_molecule',
                        args=[search_id, precursor],
                        queue=Config.MOLECULE_EXPANSION_QUEUE
                    )
                    expansion_task_ids.append(task_result.id)
            else:
                logger.info(f"[{search_id}] Max depth reached for reaction {reaction_data.get('reaction_id')}")
        
        result = {
            'search_id': search_id,
            'reaction_id': reaction_data.get('reaction_id'),
            'status': 'solved' if is_solved else 'partial',
            'precursors': precursors,
            'terminal_count': terminal_count,
            'total_precursors': len(precursors),
            'expansion_task_ids': expansion_task_ids,
            'is_solved': is_solved,
            'message': f'Processed reaction with {len(precursors)} precursors ({terminal_count} terminal)'
        }
        
        # Store result in Redis for coordination
        self.redis_client.store_reaction_result(search_id, reaction_data.get('reaction_id'), result)
        
        return result
        
    except Exception as e:
        logger.error(f"[{search_id}] Error processing reaction {reaction_data.get('reaction_id')}: {e}")
        logger.error(f"[{search_id}] Traceback: {traceback.format_exc()}")
        
        # Retry logic
        if self.request.retries < self.max_retries:
            logger.info(f"[{search_id}] Retrying reaction processing (attempt {self.request.retries + 1})")
            raise self.retry(countdown=30 * (2 ** self.request.retries))
        
        return {
            'search_id': search_id,
            'reaction_id': reaction_data.get('reaction_id'),
            'status': 'failed',
            'precursors': [],
            'error': str(e),
            'message': f'Failed to process reaction: {str(e)}'
        }

@celery_app.task(
    bind=True,
    base=ReactionProcessingTask,
    name='batch_process_reactions',
    max_retries=2
)
def batch_process_reactions_task(self, search_id: str, reactions_batch: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Process multiple reactions in a batch for efficiency.
    
    Args:
        search_id: Unique identifier for the search session
        reactions_batch: List of reaction data dictionaries
        
    Returns:
        Dictionary containing batch processing results
    """
    try:
        logger.info(f"[{search_id}] Batch processing {len(reactions_batch)} reactions")
        
        results = []
        solved_reactions = 0
        
        for reaction_data in reactions_batch:
            try:
                result = process_reaction_task.apply(args=[search_id, reaction_data]).get()
                results.append(result)
                
                if result.get('is_solved'):
                    solved_reactions += 1
                    
            except Exception as e:
                logger.error(f"[{search_id}] Error in batch processing for reaction {reaction_data.get('reaction_id')}: {e}")
                results.append({
                    'search_id': search_id,
                    'reaction_id': reaction_data.get('reaction_id'),
                    'status': 'failed',
                    'error': str(e)
                })
        
        successful = len([r for r in results if r.get('status') not in ['failed', 'error']])
        
        return {
            'search_id': search_id,
            'batch_size': len(reactions_batch),
            'successful': successful,
            'failed': len(reactions_batch) - successful,
            'solved_reactions': solved_reactions,
            'results': results,
            'message': f'Batch processing completed: {successful}/{len(reactions_batch)} successful, {solved_reactions} solved'
        }
        
    except Exception as e:
        logger.error(f"[{search_id}] Error in batch reaction processing: {e}")
        return {
            'search_id': search_id,
            'status': 'failed',
            'error': str(e),
            'message': f'Batch processing failed: {str(e)}'
        }
