"""
Redis client for distributed tree search service.
Handles queue management, state storage, and coordination data.
"""

import redis
import json
import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
from redis.sentinel import Sentinel

from config.settings import Config

logger = logging.getLogger(__name__)

class RedisClient:
    """Redis client with support for queues, state management, and coordination."""
    
    def __init__(self):
        self.redis_client = None
        self._connect()
    
    def _connect(self):
        """Establish Redis connection with sentinel support."""
        try:
            redis_config = Config.get_redis_config()
            
            if 'sentinel_hosts' in redis_config:
                # Use Redis Sentinel
                sentinel = Sentinel(redis_config['sentinel_hosts'])
                self.redis_client = sentinel.master_for(
                    redis_config['service_name'],
                    socket_timeout=5,
                    socket_connect_timeout=5,
                    socket_keepalive=True,
                    retry_on_timeout=True
                )
                logger.info("Connected to Redis via Sentinel")
            else:
                # Direct Redis connection
                self.redis_client = redis.from_url(
                    redis_config['url'],
                    socket_timeout=5,
                    socket_connect_timeout=5,
                    socket_keepalive=True,
                    retry_on_timeout=True,
                    decode_responses=True
                )
                logger.info("Connected to Redis directly")
                
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise
    
    def ping(self) -> bool:
        """Test Redis connection."""
        try:
            return self.redis_client.ping()
        except Exception as e:
            logger.error(f"Redis ping failed: {e}")
            return False
    
    # Queue Management
    def get_queue_size(self, queue_name: str) -> int:
        """Get the size of a queue."""
        try:
            return self.redis_client.llen(queue_name)
        except Exception as e:
            logger.error(f"Failed to get queue size for {queue_name}: {e}")
            return 0
    
    def push_to_queue(self, queue_name: str, data: Dict[str, Any], priority: int = 0) -> bool:
        """Push data to a queue with optional priority."""
        try:
            serialized_data = json.dumps(data)
            
            if priority > 0:
                # Use priority queue (sorted set)
                priority_queue = f"{queue_name}:priority"
                self.redis_client.zadd(priority_queue, {serialized_data: priority})
            else:
                # Use regular queue (list)
                self.redis_client.lpush(queue_name, serialized_data)
            
            return True
        except Exception as e:
            logger.error(f"Failed to push to queue {queue_name}: {e}")
            return False
    
    def pop_from_queue(self, queue_name: str, timeout: int = 0) -> Optional[Dict[str, Any]]:
        """Pop data from a queue with optional blocking."""
        try:
            # Check priority queue first
            priority_queue = f"{queue_name}:priority"
            priority_item = self.redis_client.zpopmax(priority_queue)
            
            if priority_item:
                return json.loads(priority_item[0][0])
            
            # Fall back to regular queue
            if timeout > 0:
                result = self.redis_client.brpop(queue_name, timeout=timeout)
                if result:
                    return json.loads(result[1])
            else:
                result = self.redis_client.rpop(queue_name)
                if result:
                    return json.loads(result)
            
            return None
        except Exception as e:
            logger.error(f"Failed to pop from queue {queue_name}: {e}")
            return None
    
    def batch_push_to_queue(self, queue_name: str, data_list: List[Dict[str, Any]]) -> int:
        """Push multiple items to a queue in batch."""
        try:
            pipe = self.redis_client.pipeline()
            for data in data_list:
                serialized_data = json.dumps(data)
                pipe.lpush(queue_name, serialized_data)
            
            results = pipe.execute()
            return len([r for r in results if r])
        except Exception as e:
            logger.error(f"Failed to batch push to queue {queue_name}: {e}")
            return 0
    
    # Search State Management
    def store_search_state(self, search_id: str, state: Dict[str, Any]) -> bool:
        """Store search state."""
        try:
            key = f"search_state:{search_id}"
            serialized_state = json.dumps(state, default=str)
            self.redis_client.setex(key, timedelta(hours=24), serialized_state)
            return True
        except Exception as e:
            logger.error(f"Failed to store search state for {search_id}: {e}")
            return False
    
    def get_search_state(self, search_id: str) -> Optional[Dict[str, Any]]:
        """Get search state."""
        try:
            key = f"search_state:{search_id}"
            state_data = self.redis_client.get(key)
            if state_data:
                return json.loads(state_data)
            return None
        except Exception as e:
            logger.error(f"Failed to get search state for {search_id}: {e}")
            return None
    
    def update_search_progress(self, search_id: str, progress_data: Dict[str, Any]) -> bool:
        """Update search progress metrics."""
        try:
            key = f"search_progress:{search_id}"
            pipe = self.redis_client.pipeline()
            
            for metric, value in progress_data.items():
                pipe.hset(key, metric, value)
            
            pipe.expire(key, timedelta(hours=24))
            pipe.execute()
            return True
        except Exception as e:
            logger.error(f"Failed to update search progress for {search_id}: {e}")
            return False
    
    def get_progress_metrics(self, search_id: str) -> Dict[str, Any]:
        """Get search progress metrics."""
        try:
            key = f"search_progress:{search_id}"
            metrics = self.redis_client.hgetall(key)
            
            # Convert string values to appropriate types
            converted_metrics = {}
            for k, v in metrics.items():
                try:
                    # Try to convert to int first, then float
                    if '.' in v:
                        converted_metrics[k] = float(v)
                    else:
                        converted_metrics[k] = int(v)
                except ValueError:
                    converted_metrics[k] = v
            
            return converted_metrics
        except Exception as e:
            logger.error(f"Failed to get progress metrics for {search_id}: {e}")
            return {}
    
    # Task Tracking
    def register_active_task(self, search_id: str, task_id: str, task_type: str) -> bool:
        """Register an active task for a search."""
        try:
            key = f"active_tasks:{search_id}"
            task_data = {
                'task_id': task_id,
                'task_type': task_type,
                'started_at': datetime.utcnow().isoformat()
            }
            self.redis_client.hset(key, task_id, json.dumps(task_data))
            self.redis_client.expire(key, timedelta(hours=24))
            return True
        except Exception as e:
            logger.error(f"Failed to register active task {task_id} for {search_id}: {e}")
            return False
    
    def unregister_active_task(self, search_id: str, task_id: str) -> bool:
        """Unregister an active task."""
        try:
            key = f"active_tasks:{search_id}"
            self.redis_client.hdel(key, task_id)
            return True
        except Exception as e:
            logger.error(f"Failed to unregister active task {task_id} for {search_id}: {e}")
            return False
    
    def get_active_task_count(self, search_id: str) -> int:
        """Get the number of active tasks for a search."""
        try:
            key = f"active_tasks:{search_id}"
            return self.redis_client.hlen(key)
        except Exception as e:
            logger.error(f"Failed to get active task count for {search_id}: {e}")
            return 0
    
    def get_active_task_ids(self, search_id: str) -> List[str]:
        """Get list of active task IDs for a search."""
        try:
            key = f"active_tasks:{search_id}"
            return list(self.redis_client.hkeys(key))
        except Exception as e:
            logger.error(f"Failed to get active task IDs for {search_id}: {e}")
            return []
    
    # Results Storage
    def store_reaction_result(self, search_id: str, reaction_id: str, result: Dict[str, Any]) -> bool:
        """Store reaction processing result."""
        try:
            key = f"reaction_results:{search_id}"
            serialized_result = json.dumps(result, default=str)
            self.redis_client.hset(key, reaction_id, serialized_result)
            self.redis_client.expire(key, timedelta(hours=24))
            return True
        except Exception as e:
            logger.error(f"Failed to store reaction result for {search_id}:{reaction_id}: {e}")
            return False
    
    def get_reaction_results(self, search_id: str) -> Dict[str, Dict[str, Any]]:
        """Get all reaction results for a search."""
        try:
            key = f"reaction_results:{search_id}"
            results = self.redis_client.hgetall(key)
            
            parsed_results = {}
            for reaction_id, result_data in results.items():
                try:
                    parsed_results[reaction_id] = json.loads(result_data)
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse reaction result for {reaction_id}: {e}")
                    continue
            
            return parsed_results
        except Exception as e:
            logger.error(f"Failed to get reaction results for {search_id}: {e}")
            return {}
    
    def store_search_results(self, search_id: str, results: Dict[str, Any]) -> bool:
        """Store final search results."""
        try:
            key = f"search_results:{search_id}"
            serialized_results = json.dumps(results, default=str)
            self.redis_client.setex(key, timedelta(days=7), serialized_results)
            return True
        except Exception as e:
            logger.error(f"Failed to store search results for {search_id}: {e}")
            return False
    
    def get_search_results(self, search_id: str) -> Optional[Dict[str, Any]]:
        """Get final search results."""
        try:
            key = f"search_results:{search_id}"
            results_data = self.redis_client.get(key)
            if results_data:
                return json.loads(results_data)
            return None
        except Exception as e:
            logger.error(f"Failed to get search results for {search_id}: {e}")
            return None
    
    # Cleanup
    def cleanup_search_data(self, search_id: str) -> bool:
        """Clean up all data for a search."""
        try:
            keys_to_delete = [
                f"search_state:{search_id}",
                f"search_progress:{search_id}",
                f"active_tasks:{search_id}",
                f"reaction_results:{search_id}",
                f"search_results:{search_id}"
            ]
            
            self.redis_client.delete(*keys_to_delete)
            logger.info(f"Cleaned up data for search {search_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to cleanup search data for {search_id}: {e}")
            return False
