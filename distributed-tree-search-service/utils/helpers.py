"""
Helper utilities for the distributed tree search service.
"""

import hashlib
import uuid
from typing import Optional, Any
from rdkit import Chem
import logging

logger = logging.getLogger(__name__)

def canonicalize_smiles(smiles: str) -> str:
    """
    Canonicalize a SMILES string using RDKit.
    
    Args:
        smiles: Input SMILES string
        
    Returns:
        Canonical SMILES string
    """
    try:
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            logger.warning(f"Could not parse SMILES: {smiles}")
            return smiles
        return Chem.MolToSmiles(mol, canonical=True)
    except Exception as e:
        logger.error(f"Error canonicalizing SMILES {smiles}: {e}")
        return smiles

def generate_node_id(prefix: str = "node") -> str:
    """
    Generate a unique node ID.
    
    Args:
        prefix: Prefix for the node ID
        
    Returns:
        Unique node ID string
    """
    return f"{prefix}_{uuid.uuid4().hex[:8]}"

def generate_search_id() -> str:
    """
    Generate a unique search ID.
    
    Returns:
        Unique search ID string
    """
    return f"search_{uuid.uuid4().hex}"

def hash_smiles(smiles: str) -> str:
    """
    Generate a hash for a SMILES string.
    
    Args:
        smiles: SMILES string to hash
        
    Returns:
        SHA256 hash of the SMILES
    """
    canonical_smiles = canonicalize_smiles(smiles)
    return hashlib.sha256(canonical_smiles.encode()).hexdigest()[:16]

def validate_smiles(smiles: str) -> bool:
    """
    Validate a SMILES string.
    
    Args:
        smiles: SMILES string to validate
        
    Returns:
        True if valid, False otherwise
    """
    try:
        mol = Chem.MolFromSmiles(smiles)
        return mol is not None
    except Exception:
        return False

def calculate_molecular_weight(smiles: str) -> Optional[float]:
    """
    Calculate molecular weight from SMILES.
    
    Args:
        smiles: SMILES string
        
    Returns:
        Molecular weight or None if calculation fails
    """
    try:
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            return None
        return Chem.rdMolDescriptors.CalcExactMolWt(mol)
    except Exception as e:
        logger.error(f"Error calculating molecular weight for {smiles}: {e}")
        return None

def get_atom_count(smiles: str) -> Optional[int]:
    """
    Get the number of atoms in a molecule.
    
    Args:
        smiles: SMILES string
        
    Returns:
        Number of atoms or None if calculation fails
    """
    try:
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            return None
        return mol.GetNumAtoms()
    except Exception as e:
        logger.error(f"Error getting atom count for {smiles}: {e}")
        return None

def format_reaction_smiles(reactants: str, products: str, reagents: str = "") -> str:
    """
    Format a reaction SMILES string.
    
    Args:
        reactants: Reactant SMILES (dot-separated)
        products: Product SMILES
        reagents: Reagent SMILES (optional)
        
    Returns:
        Formatted reaction SMILES
    """
    if reagents:
        return f"{reactants}>{reagents}>{products}"
    else:
        return f"{reactants}>>{products}"

def parse_reaction_smiles(reaction_smiles: str) -> tuple:
    """
    Parse a reaction SMILES string.
    
    Args:
        reaction_smiles: Reaction SMILES string
        
    Returns:
        Tuple of (reactants, reagents, products)
    """
    try:
        if '>>' in reaction_smiles:
            # No reagents
            reactants, products = reaction_smiles.split('>>')
            reagents = ""
        else:
            # With reagents
            parts = reaction_smiles.split('>')
            if len(parts) == 3:
                reactants, reagents, products = parts
            else:
                reactants = parts[0]
                reagents = ""
                products = parts[-1]
        
        return reactants.strip(), reagents.strip(), products.strip()
    except Exception as e:
        logger.error(f"Error parsing reaction SMILES {reaction_smiles}: {e}")
        return "", "", ""

def safe_float(value: Any, default: float = 0.0) -> float:
    """
    Safely convert a value to float.
    
    Args:
        value: Value to convert
        default: Default value if conversion fails
        
    Returns:
        Float value or default
    """
    try:
        return float(value)
    except (ValueError, TypeError):
        return default

def safe_int(value: Any, default: int = 0) -> int:
    """
    Safely convert a value to int.
    
    Args:
        value: Value to convert
        default: Default value if conversion fails
        
    Returns:
        Integer value or default
    """
    try:
        return int(value)
    except (ValueError, TypeError):
        return default

def truncate_string(text: str, max_length: int = 100) -> str:
    """
    Truncate a string to a maximum length.
    
    Args:
        text: Text to truncate
        max_length: Maximum length
        
    Returns:
        Truncated string
    """
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."

def format_duration(seconds: float) -> str:
    """
    Format duration in seconds to human-readable string.
    
    Args:
        seconds: Duration in seconds
        
    Returns:
        Formatted duration string
    """
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}m"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}h"
