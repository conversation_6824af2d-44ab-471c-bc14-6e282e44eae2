# Distributed Tree Search Service Setup Guide

## Overview

This service provides distributed processing for retro-synthesis tree search by parallelizing molecule expansion across multiple workers using Celery and Redis.

## Quick Start

### 1. Prerequisites

- Python 3.9+
- Redis 6.0+
- Docker & Docker Compose (optional)

### 2. Installation

```bash
# Clone or copy the service directory
cd distributed-tree-search-service

# Install dependencies
pip install -r requirements.txt

# Copy environment configuration
cp .env.example .env
# Edit .env with your configuration
```

### 3. Start Redis

```bash
# Using Docker
docker run -d -p 6379:6379 --name redis redis:7-alpine

# Or install Redis locally
# brew install redis  # macOS
# sudo apt-get install redis-server  # Ubuntu
```

### 4. Start the Service

#### Option A: Docker Compose (Recommended)

```bash
# Start all services
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f
```

#### Option B: Manual Start

```bash
# Start API server
python app.py &

# Start workers
./scripts/start-workers.sh

# Monitor with Flower
celery -A workers.celery_app flower --port=5555 &
```

### 5. Verify Installation

```bash
# Health check
curl http://localhost:8080/api/v1/health

# Submit test search
curl -X POST http://localhost:8080/api/v1/search \
  -H "Content-Type: application/json" \
  -d '{"target_smiles": "CCO", "max_routes": 10}'
```

## Integration with Main Application

### Replace Tree Search

```python
# In your main application
from distributed_tree_search_client import DistributedRetroSynthesisTreeSearch

# Replace the original tree search
tree_search = DistributedRetroSynthesisTreeSearch(
    single_step_api=single_step_api,
    terminal_checker=terminal_checker,
    disconnection_validator=disconnection_validator,
    route_scorer=route_scorer,
    synthesis_score_api=synthesis_score_api,
    config=config
)

# Use exactly the same interface
routes, error_msg = tree_search.search(target_smiles, max_routes, kwargs)
```

### Configuration

Update your main application configuration:

```python
# config/settings.py
USE_DISTRIBUTED_SEARCH = True
DISTRIBUTED_SEARCH_URL = "http://localhost:8080"
```

## Scaling

### Scale Workers

```bash
# Using Docker Compose
docker-compose up -d --scale molecule-worker=5 --scale reaction-worker=8

# Using scripts
./scripts/scale-workers.sh 5 8  # 5 molecule workers, 8 reaction workers
```

### Multiple Nodes

Deploy the service on multiple machines:

1. **Shared Redis**: Use Redis Sentinel or Redis Cluster
2. **Worker Nodes**: Deploy only workers on additional machines
3. **API Gateway**: Use load balancer for API endpoints

```bash
# On worker-only nodes
docker-compose -f docker-compose.worker.yml up -d
```

## Monitoring

### Flower Dashboard
- URL: http://localhost:5555
- Monitor worker status, task queues, and performance

### Health Endpoints
- Service health: `GET /api/v1/health`
- Queue stats: `GET /api/v1/queues/stats`

### Logs
```bash
# Docker Compose logs
docker-compose logs -f [service-name]

# Manual deployment logs
tail -f logs/*.log
```

## Performance Tuning

### Worker Configuration

```bash
# Environment variables
MAX_MOLECULE_WORKERS=10      # Number of molecule expansion workers
MAX_REACTION_WORKERS=20      # Number of reaction processing workers
MOLECULE_WORKER_CONCURRENCY=4 # Concurrent tasks per molecule worker
REACTION_WORKER_CONCURRENCY=8 # Concurrent tasks per reaction worker
```

### Queue Configuration

```bash
QUEUE_BATCH_SIZE=50          # Batch size for queue operations
MAX_QUEUE_SIZE=10000         # Maximum queue size
QUEUE_TIMEOUT=300            # Queue operation timeout
```

### Resource Limits

```bash
# Task timeouts
MOLECULE_TASK_TIMEOUT=600    # 10 minutes
REACTION_TASK_TIMEOUT=300    # 5 minutes
COORDINATION_TASK_TIMEOUT=3600 # 1 hour
```

## Troubleshooting

### Common Issues

1. **Redis Connection Failed**
   ```bash
   # Check Redis status
   redis-cli ping
   
   # Check Redis logs
   docker logs redis
   ```

2. **Workers Not Starting**
   ```bash
   # Check worker logs
   tail -f logs/*_worker.log
   
   # Verify Redis connectivity
   celery -A workers.celery_app inspect ping
   ```

3. **API Not Responding**
   ```bash
   # Check API logs
   tail -f logs/service.log
   
   # Verify port availability
   netstat -tlnp | grep 8080
   ```

4. **Tasks Stuck in Queue**
   ```bash
   # Check queue sizes
   curl http://localhost:8080/api/v1/queues/stats
   
   # Restart workers
   ./scripts/stop-workers.sh
   ./scripts/start-workers.sh
   ```

### Performance Issues

1. **Slow Processing**
   - Increase worker count
   - Check external API response times
   - Monitor Redis performance

2. **Memory Usage**
   - Reduce worker concurrency
   - Implement result streaming
   - Add memory limits to Docker containers

3. **High CPU Usage**
   - Distribute workers across multiple machines
   - Optimize validation logic
   - Use CPU-optimized instance types

## Maintenance

### Backup
```bash
# Backup Redis data
redis-cli BGSAVE

# Backup configuration
cp .env .env.backup
```

### Updates
```bash
# Pull latest changes
git pull

# Rebuild containers
docker-compose build

# Rolling update
docker-compose up -d --no-deps --build api
```

### Cleanup
```bash
# Clean old search data
# (Implement cleanup job based on your retention policy)

# Clean Docker resources
docker system prune -f
```

## Security

### Production Deployment

1. **Use HTTPS**: Configure SSL/TLS for API endpoints
2. **Authentication**: Add API key or JWT authentication
3. **Network Security**: Use VPC/private networks
4. **Redis Security**: Enable Redis AUTH and encryption
5. **Resource Limits**: Set memory and CPU limits

### Environment Variables

```bash
# Security settings
API_KEY_REQUIRED=true
REDIS_PASSWORD=your-redis-password
SSL_CERT_PATH=/path/to/cert.pem
SSL_KEY_PATH=/path/to/key.pem
```

## Support

For issues and questions:
1. Check logs in `logs/` directory
2. Review configuration in `.env` file
3. Monitor service health at `/api/v1/health`
4. Use Flower dashboard for worker monitoring
