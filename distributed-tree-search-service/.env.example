# Service Configuration
API_HOST=0.0.0.0
API_PORT=8080
DEBUG=false

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
# REDIS_SENTINEL_URL=sentinel://localhost:26379
# REDIS_SENTINEL_SERVICE_NAME=mymaster

# Worker Configuration
MAX_MOLECULE_WORKERS=10
MAX_REACTION_WORKERS=20
MOLECULE_WORKER_CONCURRENCY=4
REACTION_WORKER_CONCURRENCY=8

# Processing Configuration
QUEUE_BATCH_SIZE=50
MAX_QUEUE_SIZE=10000
QUEUE_TIMEOUT=300

# Task Timeouts (seconds)
MOLECULE_TASK_TIMEOUT=600
REACTION_TASK_TIMEOUT=300
COORDINATION_TASK_TIMEOUT=3600

# Tree Search Parameters
MAX_DEPTH=3
BEAM_WIDTH=50
PRUNING_FACTOR=0.1
MAX_ROUTES=100
BEAM_BASED_PRUNING=1

# External API Configuration
AZURE_HOSTNAME=***********

# Validation Configuration
SYNTHESIS_SCORE_THRESHOLD=1.05
MIN_FORWARD_PROB=0.7
MIN_CERTAINITY_SCORE=0.7
HEAVY_METAL_THRESHOLD=31.0

# Database Configuration
MONGODB_URL=mongodb://localhost:27017/
DATABASE_NAME=retro_synthesis

# Storage Configuration
AZURE_BLOB_CONNECTION_STRING=
AZURE_BLOB_CONTAINER_NAME=imagestorage

# Monitoring Configuration
ENABLE_METRICS=true
METRICS_PORT=9090

# Logging Configuration
LOG_LEVEL=INFO
