version: '3.8'

services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  api:
    build: .
    ports:
      - "8080:8080"
    environment:
      - REDIS_URL=redis://redis:6379/0
      - DEBUG=true
    depends_on:
      redis:
        condition: service_healthy
    command: python app.py

  molecule-worker:
    build: .
    environment:
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      redis:
        condition: service_healthy
    command: celery -A workers.celery_app worker --loglevel=INFO --concurrency=4 --hostname=molecule@%h --queues=molecule_expansion_queue
    deploy:
      replicas: 2

  reaction-worker:
    build: .
    environment:
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      redis:
        condition: service_healthy
    command: celery -A workers.celery_app worker --loglevel=INFO --concurrency=8 --hostname=reaction@%h --queues=reaction_processing_queue
    deploy:
      replicas: 3

  coordinator-worker:
    build: .
    environment:
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      redis:
        condition: service_healthy
    command: celery -A workers.celery_app worker --loglevel=INFO --concurrency=2 --hostname=coordinator@%h --queues=coordination_queue
    deploy:
      replicas: 1

  flower:
    build: .
    ports:
      - "5555:5555"
    environment:
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      redis:
        condition: service_healthy
    command: celery -A workers.celery_app flower --port=5555

volumes:
  redis_data:
