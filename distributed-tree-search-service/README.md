# Distributed Tree Search Service

A scalable microservice for retro-synthesis tree search using Celery and Redis.

## Architecture

This service provides distributed processing for retro-synthesis tree search by:
- Parallelizing molecule expansion across multiple workers
- Using Redis queues for scalable task distribution
- Providing REST API for integration with main application
- Supporting horizontal scaling of worker nodes

## Components

- **API Gateway**: REST endpoints for search requests
- **Search Coordinator**: Orchestrates distributed search
- **Molecule Workers**: Handle molecule expansion and API calls
- **Reaction Workers**: Process reactions and create precursors
- **Results Aggregator**: Collect and merge results
- **Queue Manager**: Redis-based distributed queues

## Quick Start

```bash
# Start Redis
docker run -d -p 6379:6379 redis:alpine

# Start the service
python app.py

# Start workers
celery -A workers.celery_app worker --loglevel=INFO --concurrency=4 --hostname=molecule@%h --queues=molecule_expansion_queue
celery -A workers.celery_app worker --loglevel=INFO --concurrency=8 --hostname=reaction@%h --queues=reaction_processing_queue
```

## API Endpoints

- `POST /search` - Submit tree search request
- `GET /search/{search_id}/status` - Get search progress
- `GET /search/{search_id}/results` - Get search results
- `GET /health` - Service health check

## Configuration

See `config/settings.py` for configuration options.
