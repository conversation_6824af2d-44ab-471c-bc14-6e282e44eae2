#!/bin/bash

# Start workers for distributed tree search service

set -e

echo "Starting distributed tree search workers..."

# Load environment variables
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
fi

# Default values
MOLECULE_WORKERS=${MAX_MOLECULE_WORKERS:-2}
REACTION_WORKERS=${MAX_REACTION_WORKERS:-3}
MOLECULE_CONCURRENCY=${MOLECULE_WORKER_CONCURRENCY:-4}
REACTION_CONCURRENCY=${REACTION_WORKER_CONCURRENCY:-8}

echo "Configuration:"
echo "  Molecule workers: $MOLECULE_WORKERS (concurrency: $MOLECULE_CONCURRENCY)"
echo "  Reaction workers: $REACTION_WORKERS (concurrency: $REACTION_CONCURRENCY)"
echo "  Redis URL: $REDIS_URL"

# Function to start workers
start_worker() {
    local worker_type=$1
    local queue=$2
    local concurrency=$3
    local hostname_prefix=$4
    local count=$5
    
    for i in $(seq 1 $count); do
        echo "Starting $worker_type worker $i..."
        celery -A workers.celery_app worker \
            --loglevel=INFO \
            --concurrency=$concurrency \
            --hostname=${hostname_prefix}${i}@%h \
            --queues=$queue \
            --pidfile=pids/${worker_type}_worker_${i}.pid \
            --logfile=logs/${worker_type}_worker_${i}.log \
            --detach
        
        sleep 2  # Small delay between worker starts
    done
}

# Create directories
mkdir -p pids logs

# Start molecule expansion workers
start_worker "molecule" "$MOLECULE_EXPANSION_QUEUE" "$MOLECULE_CONCURRENCY" "molecule" "$MOLECULE_WORKERS"

# Start reaction processing workers  
start_worker "reaction" "$REACTION_PROCESSING_QUEUE" "$REACTION_CONCURRENCY" "reaction" "$REACTION_WORKERS"

# Start coordination worker (single instance)
echo "Starting coordination worker..."
celery -A workers.celery_app worker \
    --loglevel=INFO \
    --concurrency=2 \
    --hostname=coordinator@%h \
    --queues=$COORDINATION_QUEUE \
    --pidfile=pids/coordinator_worker.pid \
    --logfile=logs/coordinator_worker.log \
    --detach

# Start results aggregation worker (single instance)
echo "Starting results aggregation worker..."
celery -A workers.celery_app worker \
    --loglevel=INFO \
    --concurrency=2 \
    --hostname=aggregator@%h \
    --queues=$RESULTS_AGGREGATION_QUEUE \
    --pidfile=pids/aggregator_worker.pid \
    --logfile=logs/aggregator_worker.log \
    --detach

echo "All workers started successfully!"
echo "Check logs in the 'logs' directory"
echo "Worker PIDs are stored in the 'pids' directory"

# Optional: Start Flower for monitoring
if [ "$START_FLOWER" = "true" ]; then
    echo "Starting Flower monitoring..."
    celery -A workers.celery_app flower \
        --port=5555 \
        --pidfile=pids/flower.pid \
        --logfile=logs/flower.log \
        --detach
    echo "Flower started on http://localhost:5555"
fi

echo "Use 'scripts/stop-workers.sh' to stop all workers"
