#!/bin/bash

# Stop all workers for distributed tree search service

set -e

echo "Stopping distributed tree search workers..."

# Function to stop workers by PID files
stop_workers_by_pids() {
    local pid_pattern=$1
    local worker_type=$2
    
    for pidfile in pids/${pid_pattern}*.pid; do
        if [ -f "$pidfile" ]; then
            local pid=$(cat "$pidfile")
            echo "Stopping $worker_type worker (PID: $pid)..."
            
            if kill -0 "$pid" 2>/dev/null; then
                kill -TERM "$pid"
                
                # Wait for graceful shutdown
                local count=0
                while kill -0 "$pid" 2>/dev/null && [ $count -lt 30 ]; do
                    sleep 1
                    count=$((count + 1))
                done
                
                # Force kill if still running
                if kill -0 "$pid" 2>/dev/null; then
                    echo "Force killing $worker_type worker (PID: $pid)..."
                    kill -KILL "$pid"
                fi
            fi
            
            rm -f "$pidfile"
        fi
    done
}

# Stop all worker types
stop_workers_by_pids "molecule_worker" "molecule"
stop_workers_by_pids "reaction_worker" "reaction"
stop_workers_by_pids "coordinator_worker" "coordinator"
stop_workers_by_pids "aggregator_worker" "aggregator"

# Stop Flower if running
if [ -f "pids/flower.pid" ]; then
    local flower_pid=$(cat "pids/flower.pid")
    echo "Stopping Flower (PID: $flower_pid)..."
    
    if kill -0 "$flower_pid" 2>/dev/null; then
        kill -TERM "$flower_pid"
        sleep 2
        
        if kill -0 "$flower_pid" 2>/dev/null; then
            kill -KILL "$flower_pid"
        fi
    fi
    
    rm -f "pids/flower.pid"
fi

# Alternative: Use Celery control to stop workers
echo "Sending shutdown signal to any remaining workers..."
celery -A workers.celery_app control shutdown || true

# Clean up any remaining PID files
rm -f pids/*.pid

echo "All workers stopped successfully!"

# Optional: Show any remaining Celery processes
echo "Checking for remaining Celery processes..."
pgrep -f "celery.*worker" || echo "No remaining Celery worker processes found"
