#!/bin/bash

# Deployment script for distributed tree search service

set -e

echo "Deploying distributed tree search service..."

# Configuration
SERVICE_NAME="distributed-tree-search"
DOCKER_COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env"

# Check if <PERSON><PERSON> and Docker Compose are installed
if ! command -v docker &> /dev/null; then
    echo "Error: Docker is not installed"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "Error: Docker Compose is not installed"
    exit 1
fi

# Check if environment file exists
if [ ! -f "$ENV_FILE" ]; then
    echo "Warning: .env file not found. Copying from .env.example..."
    if [ -f ".env.example" ]; then
        cp .env.example .env
        echo "Please edit .env file with your configuration before running again"
        exit 1
    else
        echo "Error: Neither .env nor .env.example found"
        exit 1
    fi
fi

# Load environment variables
export $(cat .env | grep -v '^#' | xargs)

echo "Configuration loaded from $ENV_FILE"

# Create necessary directories
mkdir -p logs pids data

# Build and start services
echo "Building Docker images..."
docker-compose -f $DOCKER_COMPOSE_FILE build

echo "Starting services..."
docker-compose -f $DOCKER_COMPOSE_FILE up -d

# Wait for services to be ready
echo "Waiting for services to be ready..."
sleep 10

# Health check
echo "Performing health check..."
max_attempts=30
attempt=0

while [ $attempt -lt $max_attempts ]; do
    if curl -f http://localhost:${API_PORT:-8080}/api/v1/health &> /dev/null; then
        echo "Service is healthy!"
        break
    fi
    
    attempt=$((attempt + 1))
    echo "Attempt $attempt/$max_attempts - waiting for service..."
    sleep 5
done

if [ $attempt -eq $max_attempts ]; then
    echo "Error: Service failed to become healthy"
    echo "Checking service logs..."
    docker-compose -f $DOCKER_COMPOSE_FILE logs --tail=50
    exit 1
fi

# Show service status
echo "Service deployment completed successfully!"
echo ""
echo "Service endpoints:"
echo "  API: http://localhost:${API_PORT:-8080}"
echo "  Health: http://localhost:${API_PORT:-8080}/api/v1/health"
echo "  Flower (monitoring): http://localhost:5555"
echo ""
echo "Useful commands:"
echo "  View logs: docker-compose -f $DOCKER_COMPOSE_FILE logs -f"
echo "  Stop services: docker-compose -f $DOCKER_COMPOSE_FILE down"
echo "  Restart services: docker-compose -f $DOCKER_COMPOSE_FILE restart"
echo "  Scale workers: docker-compose -f $DOCKER_COMPOSE_FILE up -d --scale molecule-worker=4"
echo ""
echo "Service is ready to accept requests!"
