#!/bin/bash

# Scale workers for distributed tree search service

set -e

# Default values
MOLECULE_WORKERS=${1:-2}
REACTION_WORKERS=${2:-3}

echo "Scaling distributed tree search workers..."
echo "  Molecule workers: $MOLECULE_WORKERS"
echo "  Reaction workers: $REACTION_WORKERS"

# Check if Docker Compose is available
if command -v docker-compose &> /dev/null; then
    echo "Using Docker Compose to scale workers..."
    
    docker-compose up -d \
        --scale molecule-worker=$MOLECULE_WORKERS \
        --scale reaction-worker=$REACTION_WORKERS
    
    echo "Workers scaled successfully!"
    
    # Show current status
    echo ""
    echo "Current service status:"
    docker-compose ps
    
else
    echo "Docker Compose not available. Manual scaling required."
    echo ""
    echo "To scale manually:"
    echo "1. Stop current workers: ./scripts/stop-workers.sh"
    echo "2. Update environment variables:"
    echo "   export MAX_MOLECULE_WORKERS=$MOLECULE_WORKERS"
    echo "   export MAX_REACTION_WORKERS=$REACTION_WORKERS"
    echo "3. Start workers: ./scripts/start-workers.sh"
fi

echo ""
echo "Monitor workers at: http://localhost:5555 (Flower)"
