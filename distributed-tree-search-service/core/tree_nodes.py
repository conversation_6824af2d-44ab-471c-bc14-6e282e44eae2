"""
Tree node classes for distributed tree search.
These are simplified versions adapted for the distributed service.
"""

import uuid
from enum import Enum
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, field

class NodeStatus(Enum):
    """Status of a tree node."""
    PENDING = "pending"
    PROCESSING = "processing"
    TERMINAL = "terminal"
    SOLVED = "solved"
    INVALID = "invalid"
    FAILED = "failed"

@dataclass
class TreeNode:
    """Base class for tree nodes."""
    node_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    depth: int = 0
    status: NodeStatus = NodeStatus.PENDING
    parent_id: Optional[str] = None
    children_ids: List[str] = field(default_factory=list)
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

@dataclass
class MoleculeNode(TreeNode):
    """Node representing a molecule in the synthesis tree."""
    smiles: str = ""
    synthesis_score: Optional[float] = None
    is_building_block: bool = False
    molecular_weight: Optional[float] = None
    
    def __post_init__(self):
        if not self.node_id:
            self.node_id = f"mol_{uuid.uuid4().hex[:8]}"

@dataclass
class ReactionNode(TreeNode):
    """Node representing a reaction in the synthesis tree."""
    reaction_data: Dict[str, Any] = field(default_factory=dict)
    reactants: List[str] = field(default_factory=list)
    reaction_score: Optional[float] = None
    reaction_string: str = ""
    retro_smiles: str = ""
    forward_prediction: str = ""
    reagents: str = ""
    
    def __post_init__(self):
        if not self.node_id:
            self.node_id = f"rxn_{uuid.uuid4().hex[:8]}"
    
    def is_solved(self) -> bool:
        """Check if all precursors are terminal (reaction is solved)."""
        return self.status == NodeStatus.SOLVED
    
    def get_reactant_smiles(self) -> List[str]:
        """Get list of reactant SMILES."""
        if self.retro_smiles:
            return [smiles.strip() for smiles in self.retro_smiles.split('.') if smiles.strip()]
        return self.reactants
