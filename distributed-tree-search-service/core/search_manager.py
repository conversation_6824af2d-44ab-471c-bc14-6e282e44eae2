"""
Search manager for coordinating distributed tree searches.
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from workers.celery_app import celery_app
from utils.redis_client import RedisClient
from utils.helpers import generate_search_id, validate_smiles
from api.models import SearchRequest, SearchStatus
from config.settings import Config

logger = logging.getLogger(__name__)

class SearchManager:
    """Manages distributed tree search operations."""
    
    def __init__(self):
        self.redis_client = RedisClient()
    
    def submit_search(self, search_id: str, search_request: SearchRequest) -> Dict[str, Any]:
        """
        Submit a new tree search request.
        
        Args:
            search_id: Unique search identifier
            search_request: Search request parameters
            
        Returns:
            Dictionary containing submission results
        """
        try:
            logger.info(f"Submitting search {search_id} for target: {search_request.target_smiles}")
            
            # Validate SMILES
            if not validate_smiles(search_request.target_smiles):
                raise ValueError(f"Invalid SMILES: {search_request.target_smiles}")
            
            # Prepare search request data
            search_data = {
                'search_id': search_id,
                'target_smiles': search_request.target_smiles,
                'max_routes': search_request.max_routes or Config.MAX_ROUTES,
                'max_depth': search_request.max_depth or Config.MAX_DEPTH,
                'beam_width': search_request.beam_width or Config.BEAM_WIDTH,
                'pruning_factor': search_request.pruning_factor or Config.PRUNING_FACTOR,
                'molecule_name': search_request.molecule_name,
                'created_at': datetime.utcnow().isoformat()
            }
            
            # Submit coordination task
            coordination_task = celery_app.send_task(
                'coordinate_search',
                args=[search_id, search_data],
                queue=Config.COORDINATION_QUEUE
            )
            
            # Store initial search state
            initial_state = {
                'search_id': search_id,
                'status': SearchStatus.PENDING,
                'target_smiles': search_request.target_smiles,
                'max_routes': search_data['max_routes'],
                'max_depth': search_data['max_depth'],
                'beam_width': search_data['beam_width'],
                'molecule_name': search_request.molecule_name,
                'created_at': datetime.utcnow().isoformat(),
                'updated_at': datetime.utcnow().isoformat(),
                'coordination_task_id': coordination_task.id,
                'progress': {
                    'current_depth': 0,
                    'molecules_processed': 0,
                    'reactions_processed': 0,
                    'routes_found': 0,
                    'active_tasks': 0
                }
            }
            
            self.redis_client.store_search_state(search_id, initial_state)
            
            logger.info(f"Search {search_id} submitted with coordination task {coordination_task.id}")
            
            return {
                'search_id': search_id,
                'coordination_task_id': coordination_task.id,
                'status': 'submitted',
                'message': 'Search submitted successfully'
            }
            
        except Exception as e:
            logger.error(f"Failed to submit search {search_id}: {e}")
            raise
    
    def get_search_status(self, search_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the current status of a search.
        
        Args:
            search_id: Search identifier
            
        Returns:
            Dictionary containing search status or None if not found
        """
        try:
            search_state = self.redis_client.get_search_state(search_id)
            if not search_state:
                return None
            
            # Get latest progress metrics
            progress_metrics = self.redis_client.get_progress_metrics(search_id)
            if progress_metrics:
                search_state['progress'].update(progress_metrics)
            
            # Calculate estimated completion time if running
            if search_state.get('status') == SearchStatus.RUNNING:
                search_state['estimated_completion'] = self._estimate_completion_time(search_state)
            
            return search_state
            
        except Exception as e:
            logger.error(f"Failed to get search status for {search_id}: {e}")
            return None
    
    def get_search_results(self, search_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the results of a completed search.
        
        Args:
            search_id: Search identifier
            
        Returns:
            Dictionary containing search results or None if not found
        """
        try:
            # First check if search exists
            search_state = self.redis_client.get_search_state(search_id)
            if not search_state:
                return None
            
            # Get results from Redis
            results = self.redis_client.get_search_results(search_id)
            
            if results:
                return results
            
            # If no results but search is completed, return empty results
            if search_state.get('status') == SearchStatus.COMPLETED:
                return {
                    'search_id': search_id,
                    'status': SearchStatus.COMPLETED,
                    'target_smiles': search_state.get('target_smiles'),
                    'num_routes': 0,
                    'routes': [],
                    'created_at': search_state.get('created_at'),
                    'completed_at': search_state.get('completed_at'),
                    'message': 'Search completed with no routes found'
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get search results for {search_id}: {e}")
            return None
    
    def cancel_search(self, search_id: str) -> bool:
        """
        Cancel a running search.
        
        Args:
            search_id: Search identifier
            
        Returns:
            True if cancellation was successful, False otherwise
        """
        try:
            search_state = self.redis_client.get_search_state(search_id)
            if not search_state:
                return False
            
            # Check if search can be cancelled
            current_status = search_state.get('status')
            if current_status in [SearchStatus.COMPLETED, SearchStatus.FAILED, SearchStatus.CANCELLED]:
                return False
            
            # Submit cancellation task
            cancel_task = celery_app.send_task(
                'cancel_search',
                args=[search_id],
                queue=Config.COORDINATION_QUEUE
            )
            
            logger.info(f"Search {search_id} cancellation requested with task {cancel_task.id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to cancel search {search_id}: {e}")
            return False
    
    def list_active_searches(self) -> List[Dict[str, Any]]:
        """
        List all active searches.
        
        Returns:
            List of active search summaries
        """
        try:
            # This would require scanning Redis keys, which is expensive
            # For now, return empty list - could be implemented with a search registry
            logger.warning("list_active_searches not implemented - requires search registry")
            return []
            
        except Exception as e:
            logger.error(f"Failed to list active searches: {e}")
            return []
    
    def cleanup_old_searches(self, max_age_hours: int = 24) -> int:
        """
        Clean up old search data.
        
        Args:
            max_age_hours: Maximum age of searches to keep
            
        Returns:
            Number of searches cleaned up
        """
        try:
            # This would require scanning Redis keys and checking timestamps
            # For now, return 0 - could be implemented with a cleanup job
            logger.warning("cleanup_old_searches not implemented - requires search registry")
            return 0
            
        except Exception as e:
            logger.error(f"Failed to cleanup old searches: {e}")
            return 0
    
    def _estimate_completion_time(self, search_state: Dict[str, Any]) -> Optional[str]:
        """
        Estimate completion time for a running search.
        
        Args:
            search_state: Current search state
            
        Returns:
            Estimated completion time as ISO string or None
        """
        try:
            # Simple estimation based on progress
            progress = search_state.get('progress', {})
            routes_found = progress.get('routes_found', 0)
            max_routes = search_state.get('max_routes', Config.MAX_ROUTES)
            
            if routes_found >= max_routes:
                return datetime.utcnow().isoformat()
            
            # Estimate based on current rate (simplified)
            created_at = datetime.fromisoformat(search_state.get('created_at', ''))
            elapsed = (datetime.utcnow() - created_at).total_seconds()
            
            if routes_found > 0 and elapsed > 0:
                rate = routes_found / elapsed  # routes per second
                remaining_routes = max_routes - routes_found
                estimated_seconds = remaining_routes / rate
                estimated_completion = datetime.utcnow() + timedelta(seconds=estimated_seconds)
                return estimated_completion.isoformat()
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to estimate completion time: {e}")
            return None
