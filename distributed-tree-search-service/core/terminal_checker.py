"""
Terminal checker for the distributed tree search service.
This is a placeholder that should be replaced with your actual terminal checking logic.
"""

import logging
import json
from typing import Set, Optional
from core.tree_nodes import MoleculeNode
from config.settings import Config

logger = logging.getLogger(__name__)

class TerminalChecker:
    """Checks if molecules are terminal (building blocks)."""
    
    def __init__(self):
        self.building_blocks: Set[str] = set()
        self._load_building_blocks()
    
    def _load_building_blocks(self):
        """Load building blocks from file or database."""
        try:
            # This is a placeholder - replace with your actual building blocks loading
            # For example, loading from a JSON file:
            # with open(Config.BUILDING_BLOCKS_PATH, 'r') as f:
            #     data = json.load(f)
            #     self.building_blocks = set(data.get('building_blocks', []))
            
            logger.warning("TerminalChecker._load_building_blocks is a placeholder - implement with your actual building blocks")
            
            # For testing, add some common building blocks
            self.building_blocks = {
                'CCO',  # Ethanol
                'CC(C)O',  # Isopropanol
                'C',  # <PERSON>hane
                'O',  # Water
                'CC',  # Ethane
                'CCC',  # Propane
            }
            
            logger.info(f"Loaded {len(self.building_blocks)} building blocks")
            
        except Exception as e:
            logger.error(f"Error loading building blocks: {e}")
            self.building_blocks = set()
    
    def is_terminal(self, molecule: MoleculeNode) -> bool:
        """
        Check if a molecule is terminal (building block).
        
        Args:
            molecule: Molecule node to check
            
        Returns:
            True if molecule is terminal, False otherwise
        """
        try:
            # Check against building blocks set
            if molecule.smiles in self.building_blocks:
                return True
            
            # Additional checks can be added here:
            # - Molecular weight threshold
            # - Complexity checks
            # - Commercial availability
            # - Synthesis score threshold
            
            # Example synthesis score check:
            if molecule.synthesis_score is not None:
                if molecule.synthesis_score <= Config.SYNTHESIS_SCORE_THRESHOLD:
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking if molecule {molecule.smiles} is terminal: {e}")
            return False
    
    def is_building_block(self, smiles: str) -> bool:
        """
        Check if a SMILES string represents a building block.
        
        Args:
            smiles: SMILES string to check
            
        Returns:
            True if it's a building block, False otherwise
        """
        return smiles in self.building_blocks
    
    def add_building_block(self, smiles: str):
        """
        Add a SMILES to the building blocks set.
        
        Args:
            smiles: SMILES string to add
        """
        self.building_blocks.add(smiles)
        logger.info(f"Added building block: {smiles}")
    
    def remove_building_block(self, smiles: str):
        """
        Remove a SMILES from the building blocks set.
        
        Args:
            smiles: SMILES string to remove
        """
        self.building_blocks.discard(smiles)
        logger.info(f"Removed building block: {smiles}")
    
    def get_building_blocks_count(self) -> int:
        """Get the number of loaded building blocks."""
        return len(self.building_blocks)
    
    def reload_building_blocks(self):
        """Reload building blocks from source."""
        self.building_blocks.clear()
        self._load_building_blocks()
