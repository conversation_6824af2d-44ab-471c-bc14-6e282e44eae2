from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from enum import Enum
import uuid
from datetime import datetime

class SearchStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class SearchRequest(BaseModel):
    target_smiles: str = Field(..., description="Target molecule SMILES string")
    max_routes: Optional[int] = Field(100, description="Maximum number of routes to return")
    max_depth: Optional[int] = Field(3, description="Maximum search depth")
    beam_width: Optional[int] = Field(50, description="Beam width for pruning")
    pruning_factor: Optional[float] = Field(0.1, description="Pruning factor for route selection")
    request_id: Optional[str] = Field(None, description="Optional request ID for tracking")
    molecule_name: Optional[str] = Field(None, description="Optional molecule name")
    
    class Config:
        schema_extra = {
            "example": {
                "target_smiles": "CCO",
                "max_routes": 50,
                "max_depth": 2,
                "beam_width": 30,
                "pruning_factor": 0.1,
                "molecule_name": "Ethanol"
            }
        }

class SearchResponse(BaseModel):
    search_id: str = Field(..., description="Unique search identifier")
    status: SearchStatus = Field(..., description="Current search status")
    message: str = Field(..., description="Status message")
    created_at: datetime = Field(..., description="Search creation timestamp")
    
class SearchStatusResponse(BaseModel):
    search_id: str
    status: SearchStatus
    progress: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime
    updated_at: datetime
    estimated_completion: Optional[datetime] = None
    error_message: Optional[str] = None
    
class ReactionData(BaseModel):
    step: int
    reaction_id: str
    reaction_string: str
    retro_smiles: str
    reagents: str
    forward_prediction: str
    prob_forward_1: float
    prob_forward_2: float
    score: float
    rxn_class: Dict[str, Any]
    reaction_smiles_img: Optional[str] = None
    other_information: Optional[Dict[str, Any]] = None
    reactants: List[Dict[str, Any]] = Field(default_factory=list)

class RouteData(BaseModel):
    route_id: int
    num_steps: int
    unique_id: int
    total_route_score: float
    reactions: List[ReactionData]

class SearchResultsResponse(BaseModel):
    search_id: str
    status: SearchStatus
    target_smiles: str
    num_routes: int
    routes: List[RouteData] = Field(default_factory=list)
    created_at: datetime
    completed_at: Optional[datetime] = None
    processing_time: Optional[float] = None
    error_message: Optional[str] = None

class HealthResponse(BaseModel):
    status: str
    version: str
    timestamp: datetime
    redis_connected: bool
    workers_active: Dict[str, int]
    queue_sizes: Dict[str, int]

class ErrorResponse(BaseModel):
    error: str
    message: str
    timestamp: datetime
    search_id: Optional[str] = None
