from flask import Blueprint, request, jsonify
from flask_cors import cross_origin
import uuid
import logging
from datetime import datetime
from typing import Dict, Any

from api.models import (
    SearchRequest, SearchResponse, SearchStatusResponse, 
    SearchResultsResponse, HealthResponse, ErrorResponse, SearchStatus
)
from workers.celery_app import celery_app
from core.search_manager import SearchManager
from utils.redis_client import RedisClient
from config.settings import Config

logger = logging.getLogger(__name__)

# Create Blueprint
api_bp = Blueprint('api', __name__)

# Initialize components
search_manager = SearchManager()
redis_client = RedisClient()

@api_bp.route('/health', methods=['GET'])
@cross_origin()
def health_check():
    """Health check endpoint."""
    try:
        # Check Redis connection
        redis_connected = redis_client.ping()
        
        # Get worker stats
        inspect = celery_app.control.inspect()
        active_workers = inspect.active() or {}
        
        workers_active = {
            'molecule_workers': len([w for w in active_workers.keys() if 'molecule' in w]),
            'reaction_workers': len([w for w in active_workers.keys() if 'reaction' in w]),
            'coordinator_workers': len([w for w in active_workers.keys() if 'coordinator' in w])
        }
        
        # Get queue sizes
        queue_sizes = {
            'molecule_queue': redis_client.get_queue_size(Config.MOLECULE_EXPANSION_QUEUE),
            'reaction_queue': redis_client.get_queue_size(Config.REACTION_PROCESSING_QUEUE),
            'results_queue': redis_client.get_queue_size(Config.RESULTS_AGGREGATION_QUEUE)
        }
        
        response = HealthResponse(
            status="healthy" if redis_connected else "unhealthy",
            version=Config.SERVICE_VERSION,
            timestamp=datetime.utcnow(),
            redis_connected=redis_connected,
            workers_active=workers_active,
            queue_sizes=queue_sizes
        )
        
        return jsonify(response.dict()), 200 if redis_connected else 503
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        error_response = ErrorResponse(
            error="health_check_failed",
            message=str(e),
            timestamp=datetime.utcnow()
        )
        return jsonify(error_response.dict()), 500

@api_bp.route('/search', methods=['POST'])
@cross_origin()
def submit_search():
    """Submit a new tree search request."""
    try:
        # Parse request
        data = request.get_json()
        if not data:
            raise ValueError("Request body is required")
        
        search_request = SearchRequest(**data)
        
        # Generate search ID
        search_id = search_request.request_id or str(uuid.uuid4())
        
        # Submit search to coordinator
        result = search_manager.submit_search(search_id, search_request)
        
        response = SearchResponse(
            search_id=search_id,
            status=SearchStatus.PENDING,
            message="Search request submitted successfully",
            created_at=datetime.utcnow()
        )
        
        return jsonify(response.dict()), 202
        
    except ValueError as e:
        logger.error(f"Invalid search request: {e}")
        error_response = ErrorResponse(
            error="invalid_request",
            message=str(e),
            timestamp=datetime.utcnow()
        )
        return jsonify(error_response.dict()), 400
        
    except Exception as e:
        logger.error(f"Search submission failed: {e}")
        error_response = ErrorResponse(
            error="submission_failed",
            message=str(e),
            timestamp=datetime.utcnow()
        )
        return jsonify(error_response.dict()), 500

@api_bp.route('/search/<search_id>/status', methods=['GET'])
@cross_origin()
def get_search_status(search_id: str):
    """Get the status of a search request."""
    try:
        status_data = search_manager.get_search_status(search_id)
        
        if not status_data:
            error_response = ErrorResponse(
                error="search_not_found",
                message=f"Search with ID {search_id} not found",
                timestamp=datetime.utcnow(),
                search_id=search_id
            )
            return jsonify(error_response.dict()), 404
        
        response = SearchStatusResponse(**status_data)
        return jsonify(response.dict()), 200
        
    except Exception as e:
        logger.error(f"Failed to get search status for {search_id}: {e}")
        error_response = ErrorResponse(
            error="status_check_failed",
            message=str(e),
            timestamp=datetime.utcnow(),
            search_id=search_id
        )
        return jsonify(error_response.dict()), 500

@api_bp.route('/search/<search_id>/results', methods=['GET'])
@cross_origin()
def get_search_results(search_id: str):
    """Get the results of a completed search."""
    try:
        results_data = search_manager.get_search_results(search_id)
        
        if not results_data:
            error_response = ErrorResponse(
                error="search_not_found",
                message=f"Search with ID {search_id} not found",
                timestamp=datetime.utcnow(),
                search_id=search_id
            )
            return jsonify(error_response.dict()), 404
        
        response = SearchResultsResponse(**results_data)
        return jsonify(response.dict()), 200
        
    except Exception as e:
        logger.error(f"Failed to get search results for {search_id}: {e}")
        error_response = ErrorResponse(
            error="results_fetch_failed",
            message=str(e),
            timestamp=datetime.utcnow(),
            search_id=search_id
        )
        return jsonify(error_response.dict()), 500

@api_bp.route('/search/<search_id>', methods=['DELETE'])
@cross_origin()
def cancel_search(search_id: str):
    """Cancel a running search."""
    try:
        success = search_manager.cancel_search(search_id)
        
        if not success:
            error_response = ErrorResponse(
                error="search_not_found",
                message=f"Search with ID {search_id} not found or cannot be cancelled",
                timestamp=datetime.utcnow(),
                search_id=search_id
            )
            return jsonify(error_response.dict()), 404
        
        response = {
            "search_id": search_id,
            "status": "cancelled",
            "message": "Search cancelled successfully",
            "timestamp": datetime.utcnow().isoformat()
        }
        
        return jsonify(response), 200
        
    except Exception as e:
        logger.error(f"Failed to cancel search {search_id}: {e}")
        error_response = ErrorResponse(
            error="cancellation_failed",
            message=str(e),
            timestamp=datetime.utcnow(),
            search_id=search_id
        )
        return jsonify(error_response.dict()), 500

@api_bp.route('/queues/stats', methods=['GET'])
@cross_origin()
def get_queue_stats():
    """Get queue statistics."""
    try:
        stats = {
            'molecule_queue': {
                'size': redis_client.get_queue_size(Config.MOLECULE_EXPANSION_QUEUE),
                'name': Config.MOLECULE_EXPANSION_QUEUE
            },
            'reaction_queue': {
                'size': redis_client.get_queue_size(Config.REACTION_PROCESSING_QUEUE),
                'name': Config.REACTION_PROCESSING_QUEUE
            },
            'results_queue': {
                'size': redis_client.get_queue_size(Config.RESULTS_AGGREGATION_QUEUE),
                'name': Config.RESULTS_AGGREGATION_QUEUE
            },
            'coordination_queue': {
                'size': redis_client.get_queue_size(Config.COORDINATION_QUEUE),
                'name': Config.COORDINATION_QUEUE
            }
        }
        
        return jsonify(stats), 200
        
    except Exception as e:
        logger.error(f"Failed to get queue stats: {e}")
        error_response = ErrorResponse(
            error="queue_stats_failed",
            message=str(e),
            timestamp=datetime.utcnow()
        )
        return jsonify(error_response.dict()), 500
